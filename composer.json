{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^8.1", "ext-exif": "*", "ext-gd": "*", "ext-json": "*", "awcodes/filament-tiptap-editor": "^3.0", "bezhansalleh/filament-google-analytics": "^2.0", "codeat3/blade-phosphor-icons": "^2.0", "devdojo/app": "0.11.0", "devdojo/auth": "^1.0", "devdojo/themes": "0.0.11", "dotswan/filament-code-editor": "^1.1", "filament/filament": "^3.2", "gehrisandro/tailwind-merge-laravel": "^1.2", "geowrgetudor/laravel-balance": "^0.2.4", "gotenberg/gotenberg-php": "^2.12", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^3.11", "lab404/laravel-impersonate": "^1.7.5", "laravel/folio": "^1.1", "laravel/framework": "^11.0", "laravel/pulse": "^1.3", "laravel/reverb": "^1.0", "laravel/tinker": "^2.7", "laravel/ui": "^4.5", "livewire/livewire": "^3.0", "php-http/guzzle7-adapter": "^1.1", "ralphjsmit/livewire-urls": "^1.4", "spatie/laravel-permission": "^6.4", "stripe/stripe-php": "^15.3", "tymon/jwt-auth": "@dev"}, "require-dev": {"alebatistella/duskapiconf": "^1.2", "barryvdh/laravel-debugbar": "^3.14", "fakerphp/faker": "^1.9.1", "jasonmccreary/laravel-test-assertions": "^2.4", "laravel-shift/blueprint": "^2.10", "laravel/dusk": "^8.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "6.4.0|^7.0|^8.1", "pestphp/pest": "^3.4", "pestphp/pest-plugin-laravel": "^3.0", "phpunit/phpunit": "^11.0", "spatie/laravel-error-solutions": "^1.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Wave\\": "wave/src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"dont-discover": [], "providers": ["Wave\\WaveServiceProvider"]}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover", "@php artisan storage:link", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=livewire:assets --ansi --force"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}