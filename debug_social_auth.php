<?php

// Temporary debugging script for social authentication
// Run this to test the social authentication flow step by step

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Devdojo\Auth\Http\Controllers\SocialController;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

echo "=== Social Authentication Debug Tool ===\n\n";

// Test 1: Check configuration
echo "1. Testing configuration...\n";
try {
    $socialController = new SocialController();
    $reflection = new ReflectionClass($socialController);
    $method = $reflection->getMethod('dynamicallySetSocialProviderCredentials');
    $method->setAccessible(true);
    
    echo "   Facebook configuration:\n";
    $method->invoke($socialController, 'facebook');
    echo "   - Client ID: " . config('services.facebook.client_id') . "\n";
    echo "   - Client Secret: " . (config('services.facebook.client_secret') ? 'Set' : 'Missing') . "\n";
    echo "   - Redirect: " . config('services.facebook.redirect') . "\n";
    
    echo "   Google configuration:\n";
    $method->invoke($socialController, 'google');
    echo "   - Client ID: " . config('services.google.client_id') . "\n";
    echo "   - Client Secret: " . (config('services.google.client_secret') ? 'Set' : 'Missing') . "\n";
    echo "   - Redirect: " . config('services.google.redirect') . "\n";
    
    echo "   ✓ Configuration test passed\n\n";
} catch (Exception $e) {
    echo "   ✗ Configuration test failed: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Check Socialite drivers
echo "2. Testing Socialite drivers...\n";
try {
    $method->invoke($socialController, 'facebook');
    $facebookDriver = Socialite::driver('facebook');
    echo "   ✓ Facebook driver created successfully\n";
    
    $method->invoke($socialController, 'google');
    $googleDriver = Socialite::driver('google');
    echo "   ✓ Google driver created successfully\n\n";
} catch (Exception $e) {
    echo "   ✗ Socialite driver test failed: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 3: Check database connection and user model
echo "3. Testing database and user model...\n";
try {
    $userModel = config('auth.providers.users.model');
    echo "   User model: " . $userModel . "\n";
    
    $userCount = app($userModel)->count();
    echo "   Total users in database: " . $userCount . "\n";
    
    // Check if we can create a test user
    $testUser = app($userModel)->where('email', '<EMAIL>')->first();
    if ($testUser) {
        echo "   Test user exists\n";
    } else {
        echo "   No test user found\n";
    }
    
    echo "   ✓ Database test passed\n\n";
} catch (Exception $e) {
    echo "   ✗ Database test failed: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 4: Check social provider user table
echo "4. Testing social provider user table...\n";
try {
    $socialUserCount = DB::table('social_provider_user')->count();
    echo "   Social provider users in database: " . $socialUserCount . "\n";
    echo "   ✓ Social provider user table test passed\n\n";
} catch (Exception $e) {
    echo "   ✗ Social provider user table test failed: " . $e->getMessage() . "\n\n";
}

echo "=== All basic tests completed ===\n";
echo "If all tests passed, the issue is likely in the OAuth callback process.\n";
echo "Please try social login again and check the Laravel logs for detailed error information.\n";
