<x-layouts.app>
    <x-app.container>
        <div class="max-w-screen-md mx-auto flex flex-row align-top gap-4 mb-9">
            <div class="card card-side bg-base-100 shadow-xl">
                <figure>
                    <img
                        src="{{ $document->thumbnail }}"
                        alt="Documentul tău" />
                </figure>
                <div class="card-body">
                    <h2 class="card-title">Documentul tău</h2>
                    
                    @if($template->tier_id <= 1)
                        <p>Poți să-l descarci gratuit.</p>
                        <div class="card-actions justify-end">
                            <a href="{{ route('documents.download', $document->id) }}" class="btn btn-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                                Descarcă Gratuit
                            </a>
                        </div>
                    @elseif($document->was_paid_for)
                        <p>Ai plătit deja pentru acest document.</p>
                        <div class="card-actions justify-end">
                            <a href="{{ route('documents.download', $document->id) }}" class="btn btn-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                                Descarcă
                            </a>
                        </div>
                    @elseif($can_download)
                        <p>
                            Descarcă documentul pentru {{ $template_credit_cost }} credite
                        </p>
                        <div class="card-actions justify-end">
                            <a href="{{ route('documents.download', $document->id) }}" class="btn btn-secondary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                                Descarcă
                            </a>
                        </div>
                    @else
                        <p class="text-error">Ai nevoie de {{ $template_credit_cost }} credite pentru a descărca acest document.</p>
                        <div class="card-actions justify-end">
                            <a href="{{ route('buy-credits') }}" class="btn btn-accent">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                                </svg>
                                Obține Credite
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </x-app.container>
</x-layouts.app>