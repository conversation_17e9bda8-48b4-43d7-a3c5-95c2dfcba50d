<div>
    <!-- Loading Modal -->
    <div 
        wire:loading
        class="fixed inset-0 z-50 flex items-center justify-center w-full h-full bg-black bg-opacity-50 min-h-screen min-w-screen">
        
            <div class="flex flex-col justify-center items-center gap-4 mx-auto my-auto">
                <div class="loading loading-spinner loading-lg text-primary"></div>
                <p class="text-base-content">
                    <span wire:loading.delay wire:target="saveDraft">Se salvează...</span>
                    <span wire:loading.delay wire:target="finalizeDocument">Generăm documentulspan>
                    <span wire:loading.delay wire:target="default">Please wait...</span>
                </p>
            </div>
        
    </div>

    <!-- Existing Content -->
    <div class="max-w-screen-md mx-auto flex flex-row align-top gap-4 mb-9">
        <div id="content-area" class="content-area bg-base-100 p-9 px-12 shadow-xl" style="transform: scale({{ $zoomLevel }});">
            {!! $content !!}
        </div>

        <div class="flex flex-col justify-start items-center gap-4">
                <button 
                    wire:click="saveDraft"
                    wire:loading.attr="disabled"
                    class="btn btn-square btn-info">
                    <div wire:loading.remove wire:target="saveDraft">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 256 256">
                            <path d="M219.31,72,184,36.69A15.86,15.86,0,0,0,172.69,32H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V83.31A15.86,15.86,0,0,0,219.31,72ZM168,208H88V152h80Zm40,0H184V152a16,16,0,0,0-16-16H88a16,16,0,0,0-16,16v56H48V48H172.69L208,83.31ZM160,72a8,8,0,0,1-8,8H96a8,8,0,0,1,0-16h56A8,8,0,0,1,160,72Z">
                            </path>
                        </svg>
                    </div>
                    <div wire:loading wire:target="saveDraft">
                        <div class="loading loading-spinner loading-xs"></div>
                    </div>
                </button>
                <div class="join join-vertical">
                    <button wire:click="zoomIn" class="btn btn-square btn-neutral join-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 256 256"><path d="M152,112a8,8,0,0,1-8,8H120v24a8,8,0,0,1-16,0V120H80a8,8,0,0,1,0-16h24V80a8,8,0,0,1,16,0v24h24A8,8,0,0,1,152,112Zm77.66,117.66a8,8,0,0,1-11.32,0l-50.06-50.07a88.11,88.11,0,1,1,11.31-11.31l50.07,50.06A8,8,0,0,1,229.66,229.66ZM112,184a72,72,0,1,0-72-72A72.08,72.08,0,0,0,112,184Z">
                        </path>
                        </svg>   
                    </button>
                    <span class="font-light text-center text-xs py-4  join-item">{{ round($zoomLevel * 100) }}%</span>
                    <button wire:click="zoomOut" class="btn btn-square btn-neutral join-item">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 256 256"><path d="M152,112a8,8,0,0,1-8,8H80a8,8,0,0,1,0-16h64A8,8,0,0,1,152,112Zm77.66,117.66a8,8,0,0,1-11.32,0l-50.06-50.07a88.11,88.11,0,1,1,11.31-11.31l50.07,50.06A8,8,0,0,1,229.66,229.66ZM112,184a72,72,0,1,0-72-72A72.08,72.08,0,0,0,112,184Z">
                        </path>
                        </svg>
                    </button>
                </div>
        </div>
    </div>
</div>
