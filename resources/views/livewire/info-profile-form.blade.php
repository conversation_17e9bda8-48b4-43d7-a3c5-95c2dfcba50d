<div
    x-data="{
        tabSelected: @js($initialTab ?? 1),
        tabId: $id('tabs'),
        tabButtonClicked(tabButton){
            this.tabSelected = tabButton.id.replace(this.tabId + '-', '');
            this.tabRepositionMarker(tabButton);
        },
        tabRepositionMarker(tabButton){
            this.$refs.tabMarker.style.width=tabButton.offsetWidth + 'px';
            this.$refs.tabMarker.style.height=tabButton.offsetHeight + 'px';
            this.$refs.tabMarker.style.left=tabButton.offsetLeft + 'px';
        },
        tabContentActive(tabContent){
            return this.tabSelected == tabContent.id.replace(this.tabId + '-content-', '');
        }
    }"
    x-init="$nextTick(() => {
        const activeTab = $refs.tabButtons.children[tabSelected - 1];
        tabRepositionMarker(activeTab);
    })"
    class="relative w-full max-w-3xl">
    
    <div x-ref="tabButtons" class="relative inline-grid items-center justify-center w-full h-10 grid-cols-2 p-1 text-gray-500 bg-gray-100 rounded-lg select-none">
        <button 
            :id="$id(tabId)"
            @click="tabButtonClicked($el);"
            :class="{ 'bg-[#FFA900] text-black ![--tw-bg-opacity:1]': tabSelected == 1, 'text-gray-500': tabSelected != 1 }"
            type="button" 
            class="relative z-20 inline-flex items-center justify-center w-full h-8 px-3 text-sm font-medium transition-all rounded-md cursor-pointer whitespace-nowrap"
        >Persoană fizică</button>
        <button 
            :id="$id(tabId)"
            @click="tabButtonClicked($el);"
            :class="{ 'bg-[#FFA900] text-black ![--tw-bg-opacity:1]': tabSelected == 2, 'text-gray-500': tabSelected != 2 }"
            type="button" 
            class="relative z-20 inline-flex items-center justify-center w-full h-8 px-3 text-sm font-medium transition-all rounded-md cursor-pointer whitespace-nowrap"
        >Persoană juridică</button>
        <div x-ref="tabMarker" 
             class="absolute left-0 z-10 w-1/2 h-full duration-300 ease-out" 
           
             >
            <div class="w-full h-full rounded-md shadow-sm"></div>
        </div>
    </div>
    <div class="relative w-full mt-2 content">
        <div :id="  $id(tabId + '-content')" x-show="tabContentActive($el)" class="relative">


        <!-- Your first form here -->
        <form class="h-5/6"> 
            
            <div class="card shadow-md p-4 mt-4 h-full min-h-full">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-3">
                    <select wire:model.defer="pronoun" class="input text-xs input-sm input-bordered rounded-sm col-span-1">
                        <option value="Dl.">Dl.</option>
                        <option value="Dna.">Dna.</option>
                    </select>
                   
                    <input wire:model.defer="last_name" type="text" placeholder="Nume" class="col-span-2 input input-sm input-bordered w-full rounded-sm" />
                    <input wire:model.defer="first_name" type="text" placeholder="Prenume" class="col-span-2 input input-sm input-bordered w-full rounded-sm" />
                    
                    
                    
                    <!-- Address Section -->
                    <div class="col-span-full font-semibold text-sm">Domiciliul</div>
                    <input wire:model.defer="address_city" type="text" placeholder="Localitatea" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                    <input wire:model.defer="address_county" type="text" placeholder="Sector / Județ" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                    <input wire:model.defer="address_country" type="text" placeholder="Country" class="input input-sm input-bordered w-full rounded-sm col-span-1" />

                    <input wire:model.defer="address_street" type="text" placeholder="Strada" class="input input-sm input-bordered w-full rounded-sm col-span-3" />
                    <input wire:model.defer="address_number" type="text" placeholder="Numărul" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    <input wire:model.defer="address_block" type="text" placeholder="Bloc" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    <input wire:model.defer="address_entrance" type="text" placeholder="Scara" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    <input wire:model.defer="address_floor" type="text" placeholder="Etaj" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    <input wire:model.defer="address_apt" type="text" placeholder="Apartament" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    <input wire:model.defer="postal_code" type="text" placeholder="Cod poștal" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    
                    
                    
    
                    <!-- ID Section -->
                    <div class="col-span-full font-semibold text-sm">CNP</div>
                    <input wire:model.defer="personal_id_number" type="text" placeholder="CNP" class="input input-sm input-bordered w-full rounded-sm col-span-3" />
                    <div class="lg:col-span-2 sm:hidden md:block"> </div>

                    <div class="col-span-full font-semibold text-sm">Act identitate</div>
                   
                    <select wire:model.defer="id_document_type" class="input text-xs input-sm input-bordered rounded-sm col-span-2">
                        <option value="CI.">CI</option>
                        <option value="Pașaport">Pașaport</option>
                    </select>
                   
                    <input wire:model.defer="id_document_series" type="text" placeholder="Serie" class="input input-sm input-bordered w-full rounded-sm col-span-1"  />
                    <input wire:model.defer="id_document_number" type="text" placeholder="Număr" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                    <div class="lg:col-span-1 sm:hidden md:block"></div>
                    <input wire:model.defer="id_document_issuer" type="text" placeholder="Autoritate emitentă" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                    <div class="sm:col-span-full lg:col-span-2 flex flex-row">
                    <label class="label text-nowrap ml-1 mr-2">Data emiterii</label>
                    <input wire:model.defer="id_document_issue_date" type="date" class="input input-sm input-bordered rounded-sm" />
                    </div>
                    <div class="col-span-full font-semibold text-sm">Date contact</div>
                    <input wire:model.defer="email" type="email" placeholder="Email" class="col-span-2 input input-sm input-bordered w-full rounded-sm" />
                    <input wire:model.defer="phone" type="text" placeholder="Phone" class="col-span-2 input input-sm input-bordered w-full rounded-sm" />
                    <div class="col-span-full font-semibold text-sm">Date personale</div>
                    <div class="sm:col-span-full lg:col-span-2 flex flex-row">
                        <label class="label text-nowrap mr-2">Data nașterii</label>
                        <input wire:model.defer="date_of_birth" type="date" class="input input-sm input-bordered w-full rounded-sm" />
                    </div>
                    <input wire:model.defer="place_of_birth" type="text" placeholder="Locul nașterii" class=" col-span-2 input input-sm input-bordered w-full rounded-sm" />
                    <input wire:model.defer="country_of_birth" type="text" placeholder="Țara nașterii" class="col-span-1 input input-sm input-bordered w-full rounded-sm" />
    


                    <!-- Personal ID -->
                    
    
                    
                </div>
            </div>
            <div class="flex flex-row ">
                <x-button   wire:key="save-btn-{{ $part }}-{{ now() }}"  wire:click.prevent="save('individual')">Salvează</x-button>
                <x-button wire:key="cancel-btn-{{ $part }}-{{ now() }}"  wire:click.prevent="cancel" color="neutral" >Anulează</x-button>
            </div>

        </form>

        <!-- </div> -->

    </div>

    <div :id="$id(tabId + '-content')" x-show="tabContentActive($el)" class="relative" x-cloak>

        <!-- Your second form here -->
        <form class="h-5/6">
            <div class="card shadow-md p-4 mt-4 h-full min-h-full">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-3">
                    <input wire:model.defer="company_name" type="text" placeholder="Nume firmă (formă juridică)" class="col-span-full input input-sm input-bordered w-full rounded-sm" />
                    
                        
                        <input wire:model.defer="tax_identification_number" type="text" placeholder="Cod fiscal" class=" input input-sm input-bordered w-full rounded-sm col-span-1" />
                 

                    
                    
                    <input wire:model.defer="registration_number" type="text" placeholder="Nr. RC (Jxxx/xxx/AN)" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                    <input wire:model.defer="registration_county" type="text" placeholder="Județ RC" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                  
                    <div class="col-span-full font-semibold">Sediul</div>
                    <input wire:model.defer="address_city" type="text" placeholder="Localitatea" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                    <input wire:model.defer="address_county" type="text" placeholder="Sector / Județ" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                    <input wire:model.defer="address_country" type="text" placeholder="Country" class="input input-sm input-bordered w-full rounded-sm col-span-1" />

                    <input wire:model.defer="address_street" type="text" placeholder="Strada" class="input input-sm input-bordered w-full rounded-sm col-span-3" />
                    <input wire:model.defer="address_number" type="text" placeholder="Numărul" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    <input wire:model.defer="address_block" type="text" placeholder="Bloc" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    <input wire:model.defer="address_entrance" type="text" placeholder="Scara" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    <input wire:model.defer="address_floor" type="text" placeholder="Etaj" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    <input wire:model.defer="address_apt" type="text" placeholder="Apartament" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    <input wire:model.defer="postal_code" type="text" placeholder="Cod poștal" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
    
                    <!-- Bank Details -->
                    <div class="col-span-full font-semibold">Date Bancare</div>
                    <input wire:model.defer="bank_name" type="text" placeholder="Numele băncii" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                    <input wire:model.defer="bank_city" type="text" placeholder="Sucursala" class="input input-sm input-bordered w-full rounded-sm" />
                    <input wire:model.defer="iban" type="text" placeholder="IBAN" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                <!-- Contact Details -->
                <div class="col-span-full font-semibold text-sm">Date contact</div>
                    <input wire:model.defer="email" type="email" placeholder="Email" class="col-span-2 input input-sm input-bordered w-full rounded-sm" />
                    <input wire:model.defer="phone" type="text" placeholder="Phone" class="col-span-2 input input-sm input-bordered w-full rounded-sm" />
                       <!-- Representative -->
                       <div class="col-span-full font-semibold">Reprezentant legal</div>
                    <select wire:model.defer="pronoun" class="input text-xs input-sm input-bordered rounded-sm col-span-1">
                        <option value="Dl.">Dl.</option>
                        <option value="Dna.">Dna.</option>
                    </select>
                   
                    <input wire:model.defer="last_name" type="text" placeholder="Nume" class="col-span-2 input input-sm input-bordered w-full rounded-sm" />
                    <input wire:model.defer="first_name" type="text" placeholder="Prenume" class="col-span-2 input input-sm input-bordered w-full rounded-sm" />
                    <div class="col-span-full font-semibold text-xs">Act identitate. Funcția reprezentantului</div>
                   
                    <select wire:model.defer="id_document_type" class="input text-xs input-sm input-bordered rounded-sm col-span-1">
                        <option value="CI.">CI</option>
                        <option value="Pașaport">Pașaport</option>
                    </select>
                   
                    <input wire:model.defer="id_document_series" type="text" placeholder="Serie" class="input input-sm input-bordered w-full rounded-sm col-span-1"  />
                    <input wire:model.defer="id_document_number" type="text" placeholder="Număr" class="input input-sm input-bordered w-full rounded-sm col-span-1" />
                    
                    <input wire:model.defer="id_document_issuer" type="text" placeholder="Autoritate emitentă" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                    <div class="sm:col-span-full lg:col-span-2 flex flex-row">
                    <label class="label text-nowrap ml-1 mr-2">Data emiterii</label>
                    <input wire:model.defer="id_document_issue_date" type="date" class="input input-sm input-bordered w-full rounded-sm" />
                    </div>
                    <input wire:model.defer="company_role" type="text" placeholder="Funcția" class="input input-sm input-bordered w-full rounded-sm col-span-2" />
                </div>
            </div>
            <div class="flex flex-row ">
                <x-button wire:key="save-btn-{{ $part }}-{{ now() }}"  wire:click.prevent="save('company')" >Salvează</x-button>
                <x-button wire:key="cancel-btn-{{ $part }}-{{ now() }}"  wire:click.prevent="cancel" color="neutral" >Anulează</x-button>
                
            </div>                                                                                                                         

        </form>

    </div>
</div>


