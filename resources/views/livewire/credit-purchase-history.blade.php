<div class="overflow-hidden bg-white shadow dark:bg-zinc-800 sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg font-medium leading-6 text-zinc-900 dark:text-zinc-100">
            Recent Transactions
        </h3>
    </div>
    <div class="border-t border-zinc-200 dark:border-zinc-700">
        <ul role="list" class="divide-y divide-zinc-200 dark:divide-zinc-700">
            @forelse($transactions as $transaction)
            <li class="px-4 py-4 sm:px-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="ml-3">
                            <p class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                {{ $transaction->description }}
                            </p>
                            <p class="text-sm text-zinc-500 dark:text-zinc-400">
                                {{ $transaction->created_at->format('M d, Y H:i') }}
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $transaction->amount > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $transaction->amount > 0 ? '+' : '' }}{{ $transaction->amount }} credits
                        </span>
                    </div>
                </div>
            </li>
            @empty
            <li class="px-4 py-4 sm:px-6">
                <p class="text-sm text-zinc-500 dark:text-zinc-400">
                    No transactions yet
                </p>
            </li>
            @endforelse
        </ul>
    </div>
</div>