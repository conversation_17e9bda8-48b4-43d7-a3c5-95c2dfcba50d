<div class="flex flex-col flex-1 justify-center items-center mx-auto w-full h-100 min-h-full mb-9">
    <div class="grid grid-cols-2 w-full justify-center items-stretch gap-6 mb-5">
           
        <div class="card grid-col col-span-1 bg-base-100 dark:bg-zinc-700 shadow-xl flex flex-col place-items-center min-h-80">
            <div class="card-body dark:text-white">
                <h2 class="text-xl font-semibold dark:text-white">
                @if($documentCategory == 1 || $documentCategory == 3)
                    Subsemnatul
                @elseif($documentCategory == 2 || $documentCategory == 4)
                    Partea A
                @endif
                </h2>
               
                <livewire:text-preview :infoProfileId="$infoProfileIdA" :part="'part_a'" />
                

                <div class="card-actions justify-center mb-6">
                    <x-button 
                        wire:key="button-a-{{ now() }}" 
                        wire:click="openModalA" 
                        class="w-64 bg-primary-50 text-base shadow-xl" 
                        icon="phosphor-address-book">
                        Adaugă date
                    </x-button>       
                </div>
                @if(count($userInfoProfiles) > 0)
                <div class="text-sm font-light dark:text-zinc-300">sau selectează un profil existent</div>
                <div class="flex flex-row items-center gap-2 w-full mb-6">
                    
                    <select wire:model="selectedProfileA" class="select select-bordered w-full select-text dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="">selectează un profil existent</option>
                        @foreach($userInfoProfiles as $profile)
                            <option value="{{ $profile->id }}" class="dark:bg-gray-700 dark:text-white">
                                @if($profile->entity_type == 'individual')
                                    {{ $profile->pronoun }} {{ $profile->first_name }} {{ $profile->last_name }}
                                @else
                                    {{ $profile->company_name }}
                                @endif
                            </option>
                        @endforeach
                    </select>
                    <button 
                        wire:click="saveSelectedProfile('part_a')" 
                        class="btn btn-sm btn-circle btn-primary" 
                        title="Salvează profilul selectat">
                        <i class="phosphor-check"></i>
                    </button>
                </div>
                @endif
            </div>
            @if($modalSelected=='A')
            <x-side-modal-edit wire:key="modal-a-{{ now() }}">
                <livewire:info-profile-form :part="'part_a'" wire:key="form-a-{{ now() }}" />
            </x-side-modal-edit>
            @endif
        </div>
           
        @if($slots == 2)
        <div class="card bg-base-100 dark:bg-zinc-700 shadow-xl flex flex-col place-items-start">
            <div class="card-body dark:text-white">
                <h2 class="text-xl font-semibold dark:text-white">Partea B</h2>
                <livewire:text-preview :infoProfileId="$infoProfileIdB" :part="'part_b'" />
                

                <div class="card-actions justify-center mb-6">
                    <x-button 
                        wire:key="button-b-{{ now() }}" 
                        wire:click="openModalB" 
                        class="w-64 bg-primary-50 text-base shadow-xl" 
                        icon="phosphor-address-book">
                        Adaugă date
                    </x-button>       
                </div>
                @if(count($userInfoProfiles) > 0)
                <div class="text-sm font-light dark:text-zinc-300">sau selectează un profil existent</div>
                <div class="flex flex-row items-center gap-2 w-full mb-6">
                    <select wire:model="selectedProfileB" class="select select-bordered w-full select-text dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="">alege</option>
                        @foreach($userInfoProfiles as $profile)
                            <option value="{{ $profile->id }}" class="dark:bg-gray-700 dark:text-white">
                                @if($profile->entity_type == 'individual')
                                    {{ $profile->pronoun }} {{ $profile->first_name }} {{ $profile->last_name }}
                                @else
                                    {{ $profile->company_name }}
                                @endif
                            </option>
                        @endforeach
                    </select>
                    <button 
                        wire:click="saveSelectedProfile('part_b')" 
                        class="btn btn-sm btn-circle btn-primary" 
                        title="Salvează profilul selectat">
                        <i class="phosphor-check"></i>
                    </button>
                </div>
                @endif
            </div>
            @if($modalSelected=='B')
            <x-side-modal-edit wire:key="modal-b-{{ now() }}">
                <livewire:info-profile-form :part="'part_b'" wire:key="form-b-{{ now() }}" />
            </x-side-modal-edit>
            @endif
        </div>
        @endif

        @if($slots == 3)
        <div class="card grid-col col-span-1 bg-base-100 dark:bg-zinc-700 shadow-xl flex flex-col place-items-start">
            <div class="card-body dark:text-white">
                <h2 class="text-xl font-semibold dark:text-white">Partea C</h2>
                <livewire:text-preview :infoProfileId="$infoProfileIdC" :part="'part_c'" />
                

                <div class="card-actions justify-center mb-6">
                    <x-button 
                        wire:key="button-c-{{ now() }}" 
                        wire:click="openModalC" 
                        class="w-64 bg-primary-50 text-base shadow-xl " 
                        icon="phosphor-address-book">
                        Adaugă date
                    </x-button>       
                </div>

                @if(count($userInfoProfiles) > 0)
                <div class="text-sm font-light dark:text-zinc-300">sau selectează un profil existent</div>
                <div class="flex flex-row items-center gap-2 w-full mb-6">

                    <select wire:model="selectedProfileC" class="select select-bordered w-full select-text dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="">sau selectează un profil existent</option>
                        @foreach($userInfoProfiles as $profile)
                            <option value="{{ $profile->id }}" class="dark:bg-gray-700 dark:text-white">
                                @if($profile->entity_type == 'individual')
                                    {{ $profile->pronoun }} {{ $profile->first_name }} {{ $profile->last_name }}
                                @else
                                    {{ $profile->company_name }}
                                @endif
                            </option>
                        @endforeach
                    </select>
                    <button 
                        wire:click="saveSelectedProfile('part_c')" 
                        class="btn btn-sm btn-circle btn-primary" 
                        title="Salvează profilul selectat">
                        <i class="phosphor-check"></i>
                    </button>
                </div>
                @endif
            </div>
            @if($modalSelected=='C')
            <x-side-modal-edit wire:key="modal-c-{{ now() }}">
                <livewire:info-profile-form :part="'part_c'" wire:key="form-c-{{ now() }}" />
            </x-side-modal-edit>
            @endif
        </div>
        @endif
    </div>
</div>
