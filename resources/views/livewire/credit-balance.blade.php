<div>
    @if(request()->is('profile/*'))
        {{-- Detailed view for profile page --}}
        <div class="card bg-white dark:bg-zinc-800 shadow-md">
            <div class="card-body">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-xl font-bold text-zinc-900 dark:text-zinc-200">Credit Balance</h2>
                        <p class="text-sm text-zinc-600 dark:text-zinc-400">Available for document generation</p>
                    </div>
                    <div class="bg-white dark:bg-zinc-700 border border-zinc-200 dark:border-zinc-600 rounded-lg p-4 shadow-sm">
                        <div class="text-center">
                            <div class="text-zinc-600 dark:text-zinc-400 text-sm">Current Balance</div>
                            <div class="text-2xl font-bold text-primary-500">{{ number_format($balance, 0) }}</div>
                            <div class="text-zinc-500 dark:text-zinc-500 text-xs">Credits Available</div>
                        </div>
                    </div>
                </div>

                @if($transactions->isNotEmpty())
                    <div class="h-px bg-zinc-200 dark:bg-zinc-700 my-6"></div>
                    <div class="space-y-4">
                        <h3 class="text-zinc-800 dark:text-zinc-300 font-medium">Recent Transactions</h3>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <tbody class="divide-y divide-zinc-200 dark:divide-zinc-700">
                                    @foreach($transactions as $transaction)
                                        <tr>
                                            <td class="py-3 whitespace-nowrap">
                                                <div class="font-medium text-zinc-800 dark:text-zinc-300">{{ $transaction->description }}</div>
                                                <div class="text-sm text-zinc-500 dark:text-zinc-500">{{ $transaction->created_at->diffForHumans() }}</div>
                                            </td>
                                            <td class="py-3 text-right">
                                                @if($transaction->amount > 0)
                                                    <span class="px-2 py-1 text-sm rounded-full bg-emerald-100 dark:bg-emerald-900 text-emerald-700 dark:text-emerald-300">
                                                        +{{ $transaction->amount }}
                                                    </span>
                                                @else
                                                    <span class="px-2 py-1 text-sm rounded-full bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300">
                                                        {{ $transaction->amount }}
                                                    </span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif
</div>

