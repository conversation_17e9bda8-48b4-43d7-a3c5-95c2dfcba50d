
    <div class="flex flex-col flex-1 justify-center items-center mx-auto w-full h-100 min-h-full mb-9">
        <div class="flex flex-col w-2/3 lg-w-1/3 justify-center items-center gap-6">
            <x-card class="min-w-full p-6 shadow-xl">
                <h2 class='card-title pb-6 dark:text-white'>Tipul documentului</h2>
                
                <select class="select select-primary w-full select-text dark:bg-gray-700 dark:border-gray-600 disabled:opacity-90 disabled:bg-gray-100 dark:disabled:bg-gray-700 disabled:border-gray-300 dark:disabled:border-gray-600 disabled:text-gray-800 dark:disabled:text-gray-200" 
                        wire:model.live="selectedCategory"
                        {{ $confirmedCategory ? 'disabled' : '' }}>
                    <option value="" class="dark:bg-gray-700 dark:text-white" {{ !$selectedCategory ? 'selected' : '' }}>Ce document generăm?</option>
                    @foreach ($categories as $category)
                        <option value="{{ $category->id }}" class="dark:bg-gray-700 dark:text-white" {{ $selectedCategory == $category->id ? 'selected' : '' }}>{{ $category->name }}</option>
                    @endforeach
                </select>
                
                <!-- Debug info -->
                <div class="hidden text-xs text-gray-500 dark:text-gray-400 mt-2">
                    selectedCategory: {{ $selectedCategory ? 'set' : 'not set' }}<br>
                    confirmedCategory: {{ $confirmedCategory ? 'set' : 'not set' }}
                </div>

                @if($selectedCategory && $confirmedCategory === null)
                    <div class="mt-4 flex justify-end">
                        <button wire:click="confirmCategory" class="btn btn-primary btn-sm">Confirmă selecția</button>
                    </div>
                @endif
            </x-card>
            
            @if(!empty($confirmedCategory))
                <x-card class="min-w-full p-6 shadow-xl">
                    <h2 class='card-title pb-6 dark:text-white'>Ce fel de {{$selectedCategoryName}} îți trebuie?</h2>
                    
                    <select class="select select-primary w-full select-text dark:bg-gray-700 dark:border-gray-600 disabled:opacity-90 disabled:bg-gray-100 dark:disabled:bg-gray-700 disabled:border-gray-300 dark:disabled:border-gray-600 disabled:text-gray-800 dark:disabled:text-gray-200" 
                            wire:model.live="selectedSubcategory"
                            {{ $confirmedSubcategory ? 'disabled' : '' }}>
                        <option value="" class="dark:bg-gray-700 dark:text-white" {{ !$selectedSubcategory ? 'selected' : '' }}>Alege categoria</option>
                        @foreach ($subcategories as $subcategory)
                            <option value="{{ $subcategory->id }}" class="dark:bg-gray-700 dark:text-white" {{ $selectedSubcategory == $subcategory->id ? 'selected' : '' }}>{{ $subcategory->name }}</option>
                        @endforeach
                    </select>
                    
                    @if(!empty($selectedSubcategory) && empty($confirmedSubcategory))
                        <div class="mt-4 flex justify-end">
                            <button wire:click="confirmSubcategory" class="btn btn-primary btn-sm">Confirmă selecția</button>
                        </div>
                    @endif
                </x-card>
            @endif
            
            @if(!empty($confirmedSubcategory))
                <x-card class="min-w-full p-6 shadow-xl">
                    <h2 class='card-title pb-6 dark:text-white'>Alege tipul de document</h2>
                    
                    <select class="select select-primary w-full select-text dark:bg-gray-700 dark:border-gray-600 disabled:opacity-90 disabled:bg-gray-100 dark:disabled:bg-gray-700 disabled:border-gray-300 dark:disabled:border-gray-600 disabled:text-gray-800 dark:disabled:text-gray-200" 
                            wire:model.live="selectedTemplate">
                        <option value="" class="dark:bg-gray-700 dark:text-white" {{ !$selectedTemplate ? 'selected' : '' }}>Alege un model de document</option>
                        @foreach ($templates as $template)
                            <option value="{{ $template->id }}" class="dark:bg-gray-700 dark:text-white" {{ $selectedTemplate == $template->id ? 'selected' : '' }}>{{ $template->name }}</option>
                        @endforeach
                    </select>
                </x-card>
            @endif
            
            <div class="flex justify-center mt-4">
                <a href="{{ route('editor') }}" class="btn btn-secondary">Resetează selecțiile</a>
            </div>
        </div>
    </div>

