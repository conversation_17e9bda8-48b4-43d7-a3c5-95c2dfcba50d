<div class="max-w-screen-md mx-auto flex flex-row align-top gap-4 mb-9">
<div class="card card-side bg-base-100 shadow-xl">
  <figure>
    <img
      src="{{  $document_preview_url }}"
      alt="Documentul tău" />
  </figure>
  <div class="card-body">
    <h2 class="card-title">Documentul tău e gata!</h2>
    
    @if($template->is_free)
      <p>Po<PERSON>i să-l descarci gratuit.</p>
      <div class="card-actions justify-end">
        <button wire:click="downloadDocument" class="btn btn-primary">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
          Descarcă Gratuit
        </button>
      </div>
    @elseif($can_download)
      <p>
       
          Descarcă documentul pentru {{ $template_credit_cost }} credite
       
      </p>
      <div class="card-actions justify-end">
        <button wire:click="downloadDocument" class="btn btn-secondary">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
          Descarcă
        </button>
      </div>
    @else
      <p class="text-error">Ai nevoie de {{ $template_credit_cost }} credite pentru a descărca acest document.</p>
      <div class="card-actions justify-end">
        <a href="{{ route('buy-credits') }}" class="btn btn-accent">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
          </svg>
          Obține Credite
        </a>
      </div>
    @endif
  </div>
</div>


<!-- Success Modal -->
<div x-data="{ 
    open: false,
    init() {
        window.addEventListener('documentDownloaded', () => {
            this.open = true;
        });
    }
}" 
    x-show="open" 
    x-cloak
    class="fixed inset-0 z-50 overflow-y-auto" 
    style="display: none;">
    <div class="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div x-show="open" 
            x-transition:enter="ease-out duration-300" 
            x-transition:enter-start="opacity-0" 
            x-transition:enter-end="opacity-100" 
            x-transition:leave="ease-in duration-200" 
            x-transition:leave-start="opacity-100" 
            x-transition:leave-end="opacity-0" 
            class="fixed inset-0 transition-opacity">
            <div class="absolute inset-0 bg-black opacity-50"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;
        <div x-show="open" 
            x-transition:enter="ease-out duration-300" 
            x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
            x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" 
            x-transition:leave="ease-in duration-200" 
            x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" 
            x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" 
            class="inline-block px-4 pt-5 pb-4 overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6" 
            role="dialog">
            <div class="flex flex-col justify-between w-full mt-2">
                <div class="flex items-center justify-center mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-center leading-6 text-gray-900">Descărcare reușită!</h3>
                <div class="mt-2">
                    <p class="text-sm text-center text-gray-500">
                        Documentul tău a fost descărcat cu succes. Vei fi redirecționat către panoul de control.
                    </p>
                </div>
                <div class="mt-5 sm:mt-6 flex justify-center">
                    <button wire:click="redirectToDashboard" 
                        @click="open = false" 
                        type="button" 
                        class="btn btn-primary">
                        Înapoi la panou
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>