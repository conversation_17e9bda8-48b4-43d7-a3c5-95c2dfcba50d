<svg {{ $attributes->merge(['class' => 'text-gray-900 dark:text-white']) }} 
    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 27 27" fill="none">
    <style type="text/css">

        <![CDATA[
            .fil0 {fill:url(#id0)}
           ]]>
        </style>
        
    
        <linearGradient id="id0" gradientUnits="userSpaceOnUse" x1="2" y1="23.04" x2="10.92" y2="6.33">
            <stop offset="0" style="stop-opacity:1; stop-color:#FF8800"/>
            <stop offset="1" style="stop-opacity:1; stop-color:#FFAA00"/>
           </linearGradient>
         
    
    <path class="fil0" d="M14.5 0l6.54 0c1.1,0 2.01,0.9 2.01,2l0 10.13c-0.97,-0.9 -2.54,-1.19 -4.59,-0.27 0.24,3.98 -3.03,5.31 -6.4,5.37 0.24,-2.07 0.58,-4.28 0.95,-7.14 0.41,-2.93 0.96,-5.79 1.33,-8.38 0.09,-0.63 0.14,-1.2 0.16,-1.71zm-12.5 0l4.31 0c0.18,2 -0.4,5.4 -1.06,10.53 -0.38,3.03 -0.89,6.19 -1.6,10.04 1.36,-0.48 2.69,-0.71 3.88,-0.71 2.18,0 5.52,0.95 9.53,0.95 2.78,0 4.82,-0.93 5.99,-2.47l0 2.7c0,1.1 -0.91,2 -2.01,2l-19.04 0c-1.1,0 -2,-0.9 -2,-2l0 -19.04c0,-1.1 0.9,-2 2,-2z"/>
    
</svg>
