<?php

use Illuminate\Support\Facades\Log;

function processDocumentContent(string $content): string
{
    if (strpos($content, '<h2') === false) {
        return $content;
    }

    // Split at h2 tags
    $pattern = '/(<h2.*?>)/i';
    $parts = preg_split($pattern, $content, -1, PREG_SPLIT_DELIM_CAPTURE);

    // Get everything before first h2
    $result = $parts[0];
    
    // Start the ordered list
    $result .= "\n<ol><li>";
    
    // Add each section with proper li tags
    for ($i = 1; $i < count($parts); $i += 2) {
        if ($i > 1) {
            $result .= "</li><li>";
        }
        $result .= $parts[$i] . $parts[$i + 1];
    }
    
    // Close the list
    $result .= "</li></ol>";
    
        $wrappedContent = '<!DOCTYPE html><html><head><meta charset="utf-8"></head><body>' . $result . '</body></html>';

        $dom = new DOMDocument();
        @$dom->loadHTML($wrappedContent, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        $xpath = new DOMXPath($dom);

        /**
         * 1) For each <li> that contains an <h2>,
         *    find the first <ol> child and add class="paragraph-counter".
         */
        $liNodes = $xpath->query('//li[h2]');
        foreach ($liNodes as $li) {
            // Get the first <ol> inside this <li>
            $firstOl = $xpath->query('./ol[1]', $li);
            if ($firstOl->length > 0) {
                // Add the paragraph-counter class
                $existingClass = $firstOl->item(0)->getAttribute('class');
                $newClass = trim($existingClass . ' paragraph-counter');
                $firstOl->item(0)->setAttribute('class', $newClass);

                /**
                 * 2) For every <ol> that is nested within this newly-labeled <ol>'s <li>,
                 *    add class="line-counter".
                 */
                $nestedOls = $xpath->query('.//li/ol', $firstOl->item(0));
                foreach ($nestedOls as $nestedOl) {
                    $existingClass = $nestedOl->getAttribute('class');
                    $newClass = trim($existingClass . ' line-counter');
                    $nestedOl->setAttribute('class', $newClass);
                }
            }
        }

        // Extract only the part inside <body>.
        $bodyNode = $dom->getElementsByTagName('body')->item(0);
        $processedHtml = '';
        if ($bodyNode) {
            foreach ($bodyNode->childNodes as $child) {
                $processedHtml .= $dom->saveHTML($child);
            }
        }

        return $processedHtml;



}



$preppedContent = processDocumentContent($finalContent);




?>

@extends('layouts.docs')

@section('title', 'Contract Document')

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/docs.css') }}">
    <link rel="stylesheet" href="{{ asset('css/interface.css') }}">
@endpush

@push('scripts')
    <script src="{{ asset('js/docs/paged.polyfill.min.js') }}"></script> 
  
@endpush

@section('content')

<div id='runningHeader'>
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xodm="http://www.corel.com/coreldraw/odm/2003" xml:space="preserve" width="242px" height="58px" version="1.1" class="py-0 my-0" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd" viewBox="0 0 728 173.26">
    <defs>
    <style type="text/css">
    <![CDATA[
        .fil1 {fill:#BCDB0F}
        .fil0 {fill:black}
    ]]>
    </style>
    </defs>
    <g id="logo">
    <path id="text" class="fil0" d="M226.11 64.66l58.1 0.01 0 13.1 -60.3 -0.01c-8.67,0 -15.28,-2.46 -19.85,-7.35 -4.56,-4.91 -6.85,-11.73 -6.85,-20.46 0,-8.73 2.29,-15.55 6.85,-20.45 4.57,-4.89 11.18,-7.34 19.85,-7.34l60.3 0.01 0 13.11 -58.1 -0.02c-3.07,0 -5.6,0.86 -7.59,2.6 -0.94,0.79 -2.14,2.66 -3.61,5.6l-0.1 0.19 69.4 0.02 0 12.5 -69.4 -0.02 0.1 0.3c1.33,2.87 2.53,4.74 3.61,5.61 1.99,1.74 4.52,2.6 7.59,2.6zm-59.9 -22.92c0,-1.87 -0.6,-3.32 -1.8,-4.4 -0.87,-0.79 -2.2,-1.2 -4,-1.2l-41.5 -0.11 0 41.7 -16.71 0 0 -55.6 61.01 -0.19c6.53,0 11.53,1.84 15,5.51 3.13,3.33 4.7,7.17 4.7,11.49l0 5.81 -16.7 0 0 -3.01 0 0zm-80.01 -19.61l0 13.89 -57.3 -0.01c-3.87,0 -6.8,1.39 -8.81,4.19 -1.72,2.41 -2.59,5.64 -2.59,9.71 0,4.06 0.87,7.3 2.59,9.7 2.01,2.8 4.94,4.2 8.81,4.2l57.3 0.02 0 13.89 -59.5 -0.01c-8.67,0 -15.29,-2.46 -19.85,-7.35 -4.57,-4.91 -6.85,-11.73 -6.85,-20.46 0,-8.74 2.28,-15.55 6.85,-20.45 4.56,-4.9 11.18,-7.35 19.85,-7.35l59.5 0.03 0 0zm582.94 42.64l58.1 0.02 0 13.1 -60.3 -0.01c-8.67,-0.01 -15.28,-2.46 -19.85,-7.37 -4.57,-4.89 -6.85,-11.71 -6.85,-20.44 0,-8.74 2.28,-15.56 6.85,-20.46 4.57,-4.9 11.18,-7.35 19.85,-7.34l60.3 0.01 0 13.1 -58.1 -0.01c-3.07,0 -5.6,0.87 -7.6,2.6 -0.93,0.8 -2.13,2.67 -3.6,5.6l-0.1 0.2 69.4 0.02 0 12.5 -69.4 -0.02 0.1 0.3c1.33,2.87 2.53,4.74 3.6,5.6 2,1.73 4.53,2.6 7.6,2.6zm-118.31 -0.83l32.11 0.12c11.1,-0.11 25.47,1.78 24.66,-17.73l-0.37 -24.08 16.71 0.01c0,10.25 -0.11,16.16 -0.11,26.41 0.4,16.18 -7.44,29.1 -27.66,29.04l-62.04 0.13 0 -55.6 16.7 0 0 41.7zm-33.4 -47.31l-16.7 -0.01 0 -13.29 16.7 0 0 13.3zm0 61.21l-16.7 -0.02 0 -55.59 16.7 0 0 55.61zm-31.39 -0.02l-50.41 -0.01c-5.87,0 -10.11,-1.33 -12.71,-3.99 -1.66,-1.75 -2.76,-3.71 -3.29,-5.91 -0.41,-1.47 -0.6,-3.73 -0.6,-6.8l0 -25.8 -10 0 0 -13.1 10 0 0 -22.21 16.7 0.01 0 22.2 50.31 0.01 0 13.11 -50.31 -0.02 0 21.7c0,3.07 0.69,5.18 2.1,6.3 1.13,0.94 3.03,1.41 5.7,1.41l42.51 0.01 0 13.09 0 0zm-111.22 -13.12l0 -8.51 -53.4 -0.01c-3.53,0 -5.3,1.44 -5.3,4.3 0,2.8 1.77,4.21 5.3,4.21l53.4 0.01 0 0zm-71.51 -29.42l0 -13.1 66.71 0.01c6.6,0.01 11.83,1.74 15.7,5.2 2.4,2.14 4.03,4.64 4.9,7.51 0.6,2.13 0.9,5.14 0.9,9l0 33.9 -74.5 -0.02c-5.46,0 -9.77,-1.5 -12.9,-4.5 -3.14,-3 -4.7,-7.2 -4.7,-12.6 0,-5.41 1.58,-9.59 4.75,-12.55 3.16,-2.97 7.45,-4.45 12.85,-4.45l57.81 0.01 0 -0.49c0,-4.13 -1.54,-6.63 -4.61,-7.5 -0.93,-0.27 -2.37,-0.4 -4.3,-0.4l-62.61 -0.02 0 0zm408.17 112.7l0 -8.4 -52.79 -0.01c-3.5,0 -5.24,1.41 -5.24,4.24 0,2.77 1.74,4.15 5.24,4.15l52.79 0.02zm-70.69 -29.08l0 -12.96 65.95 0.01c6.53,0.01 11.7,1.73 15.53,5.16 2.37,2.1 3.98,4.57 4.84,7.4 0.59,2.12 0.89,5.09 0.89,8.91l0 33.52 -73.66 -0.02c-5.41,0 -9.66,-1.49 -12.76,-4.46 -3.1,-2.96 -4.65,-7.11 -4.65,-12.45 0,-5.34 1.57,-9.48 4.7,-12.41 3.13,-2.93 7.37,-4.4 12.71,-4.4l57.14 0.01 0 -0.49c0,-4.09 -1.51,-6.56 -4.54,-7.41 -0.92,-0.27 -2.34,-0.4 -4.25,-0.4l-61.89 -0.02 -0.01 0.01zm-20.37 -18.51l-16.51 0 0 -13.14 16.51 0 0 13.14zm0 60.52l-16.51 0 0 -54.98 16.51 0 0 54.98zm-99.66 -41.26c-3.82,0 -6.72,1.38 -8.7,4.15 -1.72,2.38 -2.57,5.57 -2.57,9.59 0,4.03 0.85,7.22 2.57,9.59 1.98,2.77 4.88,4.16 8.7,4.16l49.34 0.02 0 -27.49 -49.34 -0.02 0 0zm-2.18 41.24c-11.27,-0.01 -19.01,-4.17 -23.23,-12.47 -2.12,-4.16 -3.17,-9.16 -3.17,-15.03 0,-11.93 4.09,-20.04 12.26,-24.32 4.03,-2.11 8.74,-3.16 14.14,-3.16l51.52 0.01 0 -21.95 16.5 0.01 0 76.92 -68.02 -0.01 0 0zm-98.87 -12.99l57.45 0.02 0 12.96 -59.62 -0.03c-8.57,0 -15.11,-2.42 -19.63,-7.27 -4.51,-4.84 -6.77,-11.58 -6.77,-20.21 0,-8.64 2.26,-15.38 6.77,-20.23 4.52,-4.83 11.06,-7.26 19.63,-7.26l59.62 0.02 0 12.95 -57.45 -0.02c-3.03,0 -5.54,0.86 -7.51,2.58 -0.92,0.79 -2.11,2.63 -3.57,5.53l-0.09 0.2 68.62 0.02 0 12.35 -68.62 -0.01 0.09 0.29c1.33,2.84 2.51,4.68 3.57,5.54 1.97,1.71 4.48,2.57 7.51,2.57l0 0zm-60.31 -22.55c0,-3.83 -1.91,-5.74 -5.74,-5.74l-26.99 0 0 41.22 -16.51 0 0 -41.23 -32.24 -0.01 0 41.23 -16.5 -0.01 0 -54.96 95.01 0.02c6.6,0 11.57,1.79 14.94,5.34 3.02,3.23 4.54,7.06 4.54,11.48l0 38.15 -16.51 0 0 -35.49 0 0z"></path>
    <path id="flag" class="fil1" d="M1.01 84.15c0,0 26.74,14.01 76.02,13.49 49.28,-0.53 48.03,-14.08 93.56,-14.82 45.52,-0.73 79.35,16.23 79.35,16.23l0 73.45c0,0 -38.42,-16.17 -78.94,-14.91 -40.51,1.26 -54.71,14.22 -94.81,15.59 -40.09,1.37 -75.18,-15.59 -75.18,-15.59l0 -73.44 0 0z"></path>
    </g>
    </svg>

</div>

<h1>{{$templateTitle}}</h1>

{!! $preppedContent !!}

@if($document_category == 2)
<div class="signature-div">
    <div class="signature-column">
        <div class="signature-line signature-header">FURNIZOR</div>
        <div class="signature-line signature-name">{{$partyA_rep_name}}</div>
        <div class="signature-line signature-role">{{$partyA_rep_role}}</div>
    </div>
    <div class="signature-column">
        <div class="signature-line signature-header">BENEFICIAR</div>
        <div class="signature-line signature-name">{{$partyB_rep_name}}</div> 
        <div class="signature-line signature-role">{{$partyB_rep_role}}</div>
    </div>
</div>
@else 
<div class="signature-div">
    <div class="signature-column">
        <div class="signature-line signature-header">Semnătură</div>
        <div class="signature-line signature-name">{{$partyA_rep_name}}</div>
    
    </div>
</div>
@endif

@endsection


