/* Inline Inputs and Selects */
.inline-input,
.inline-select,
.inline-checkbox {
    border: none;
    border-bottom: 1px solid currentColor;
    font: inherit;
    color: inherit;
    background: transparent;
    padding: 0 2px;
    margin: 0;
    width: auto;
    display: inline-block;
    vertical-align: middle;
}

.conditional-block label input[type="checkbox"] {
   @apply: checkbox checkbox-xs ;
}
.inline-input:focus,
.inline-select:focus {
    outline: none;
    border-bottom: 1px solid blue;
}

.inline-checkbox {
    margin-right: 4px;
    margin-top: 0.15em;
}

.inline-checkbox:focus {
    outline: 1px solid blue;
}

/* Styling for the content */
.content-area {
    font-size: 14.7px; /* 11pt converted to pixels */
    line-height: 1.25;
    text-align: left;
    max-width: 800px; /* Approximate width of an A4 page */
    margin: 0 auto;
}

.content-area p {
    margin-bottom: calc(2 * 1.25em); /* 2 line heights */
}

/* Header sizes increasing by factor of 1.618 */
.content-area h4 {
    font-size: 14.7px; /* Base font size */
    margin-top: 1em;
    margin-bottom: 0.5em;
}

.content-area h3 {
    font-size: calc(14.7px * 1.618); /* h4 size * 1.618 */
    margin-top: 1.25em;
    margin-bottom: 0.6em;
}

.content-area h2 {
    font-size: calc(14.7px * 1.618 * 1.618); /* h3 size * 1.618 */
    margin-top: 1.5em;
    margin-bottom: 0.7em;
}

.content-area h1 {
    font-size: calc(14.7px * 1.618 * 1.618 * 1.618); /* h2 size * 1.618 */
    margin-top: 1.75em;
    margin-bottom: 0.8em;
}

.content-area h1,
.content-area h2,
.content-area h3,
.content-area h4 {
    font-weight: bold;
}

.content-area ul {
    list-style-type: disc;
    margin-left: 1.5em;
    margin-bottom: calc(2 * 1.25em);
}

.content-area li {
    margin-bottom: 0.5em;
}

/* Conditional Block Styling */
.conditional-block {
   /* display: flex;
    align-items: center;
    margin-bottom: calc(2 * 1.25em); /* Match paragraph spacing */
}

.conditional-block .inline-checkbox {
    margin-right: 8px;
 /*   margin-top: 4px; */
}

.conditional-block > *:not(.inline-checkbox) {
    flex: 1;
}

/* Select Options Styling */
.inline-select {
    color: inherit;
    background-color: transparent;
}

.inline-select option {
    color: black;
    background-color: white;
}

@media (prefers-color-scheme: dark) {
    .inline-select option {
        color: white;
        background-color: #2d2d2d; /* Dark background */
    }
}

/* Initialize counters */
#content-area {
    counter-reset: section;
}

/* Section numbering */

#content-area h2 {
    counter-reset: subsection paragraph;
}

#content-area h2::before {
    counter-increment: section;
    content: counter(section) ". ";
}

#content-area h3 {
    counter-reset: paragraph;
}

#content-area h3::before {
    counter-increment: subsection;
    content: counter(section) "." counter(subsection) ". ";
}

#content-area p {
    counter-increment: paragraph;
}

#content-area p::before {
    content: counter(section) "." counter(subsection) "." counter(paragraph)
        ". ";
}