<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" x-data="{ theme: localStorage.getItem('theme') || 'light' }" :class="{ 'dark': theme === 'dark' }">
<head>
    @include('theme::partials.head', ['seo' => ($seo ?? null) ])
    @include('theme::partials.theme-init')
</head>
<body 
    class="flex flex-col min-h-screen bg-white text-zinc-900 dark:bg-zinc-900 dark:text-white"
    x-init="$watch('theme', value => localStorage.setItem('theme', value))"
>
    <x-marketing.elements.header />
    
    <main class="flex-grow">
        {{ $slot }}
    </main>

    <x-marketing.elements.footer />
    
    @livewireScripts
    @stack('scripts')
</body>
</html>
