<?php
    use function <PERSON><PERSON>\Folio\{middleware, name};
	middleware('auth');
    name('dashboard');
    
    use App\Models\DocumentCategory;
    use Illuminate\Support\Facades\Auth;
    
    // Use auth() helper instead of Auth::user() directly
    $categories = DocumentCategory::withCount(['documents' => function($query) {
        $query->where('user_id', auth()->id());
    }])->get();
?>

<x-layouts.app>
    <x-app.container x-data class="lg:space-y-6" x-cloak>
        
        <x-app.alert id="dashboard_alert" class="hidden lg:flex">Bine ai venit în panoul de control. Aici poți gestiona documentele și seturile tale de date. <a href="https://devdojo.com/wave/docs" target="_blank" class="mx-1 underline">Află mai multe</a> despre funcționalitățile disponibile.</x-app.alert>

        <div class="flex flex-col w-full mt-6 space-y-5 md:flex-row lg:mt-0 md:space-y-0 md:space-x-5">
            <!-- User Greeting Card - Top Left -->
            <x-app.dashboard-card
                href="{{ route('wave.profile', ['username' => auth()->user()->username]) }}"
                title="Bine ai venit, {{ auth()->user()->name }}!"
                description="Accesează profilul tău pentru a-ți gestiona informațiile personale și preferințele."
                link_text="Vizualizează Profilul"
                image="/wave/img/docs.png"
            />


        </div>


		        <div class="flex flex-col w-full mt-5 space-y-5 md:flex-row md:space-y-0 md:mb-0 md:space-x-5">
            <!-- Documents Link Card -->
            <div class="flex flex-col flex-1 overflow-hidden bg-white rounded-xl border border-zinc-200/70 dark:border-zinc-700 dark:bg-zinc-800 hover:scale-[1.01] duration-300 ease-out">
                <div class="flex flex-col p-5 h-full">
                    <div class="flex flex-col space-y-2">
                        <h3 class="text-lg font-semibold text-zinc-900 dark:text-white">Documentele Mele</h3>
                        <p class="text-sm text-zinc-600 dark:text-zinc-400">
                            Accesează și gestionează toate documentele tale generate.
                        </p>
                        
                        <div class="mt-3 grid grid-cols-1 sm:grid-cols-2 gap-2">                         
                            @foreach ($categories as $category)
                                <a href="{{ url('arhiva/' . $category->slug) }}" 
                                   class="flex items-center p-2 rounded-lg hover:bg-zinc-50 dark:hover:bg-zinc-700/40 transition-colors">
                                    <x-phosphor-folder-open class="w-5 h-5 mr-2 text-primary-500" />
                                    <span class="text-sm text-zinc-800 dark:text-zinc-200">{{ $category->name }}</span>
                                    <span class="ml-auto text-xs text-zinc-500 dark:text-zinc-400">({{ $category->documents_count }})</span>
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Data Sets Link Card -->
            <div class="flex flex-col flex-1 overflow-hidden bg-white rounded-xl border border-zinc-200/70 dark:border-zinc-700 dark:bg-zinc-800 hover:scale-[1.01] duration-300 ease-out">
                <div class="flex flex-col p-5 h-full">
                    <div class="flex flex-col space-y-2">
                        <h3 class="text-lg font-semibold text-zinc-900 dark:text-white">Seturi de Date</h3>
                        <p class="text-sm text-zinc-600 dark:text-zinc-400">
                            Gestionează seturile tale de date salvate pentru generarea rapidă de documente.
                        </p>
                    </div>
                    <div class="flex mt-auto pt-4">
                        <a href="{{ route('seturidedate') }}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-zinc-700 bg-white border border-zinc-300 rounded-md shadow-sm hover:bg-zinc-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-zinc-700 dark:text-zinc-200 dark:border-zinc-600 dark:hover:bg-zinc-600">
                            <x-phosphor-database class="w-5 h-5 mr-2" />
                            Vezi Seturile de Date
                        </a>
                    </div>
                </div>
            </div>
        </div>
		<div class="flex flex-col flex-1 overflow-hidden bg-white rounded-xl border border-zinc-200/70 dark:border-zinc-700 dark:bg-zinc-800">
                <div class="flex flex-col p-5 h-full">
                    <div class="flex flex-col space-y-2">
                        <h3 class="text-lg font-semibold text-zinc-900 dark:text-white">Credit și abonamente</h3>
						<div class="flex flex-row space-y-2">
							<p class="text-sm w-1/2 text-zinc-600 dark:text-zinc-400">
								@subscriber
									Ești abonat la planul <strong>{{ auth()->user()->plan()->name }}</strong> ({{ auth()->user()->planInterval() }})
								@else
									Contul tău nu are un abonament activ. Pentru a beneficia de funcționalități premium, <a href="{{ route('settings.subscription') }}" class="underline">abonează-te la un plan</a>.
								@endsubscriber
							</p>
                        
                        	<!-- Credit Balance -->
							<div class="mt-2 w p-3 bg-zinc-50 dark:bg-zinc-700/40 rounded-lg">
								<div class="flex justify-between items-center">
									<span class="text-sm text-zinc-600 dark:text-zinc-300">Credite disponibile:</span>
									<span class="text-lg font-bold text-primary-500">
										<livewire:credit-balance-simple />
									</span>
								</div>
							</div>
						</div>
                    </div>
                    
                    <div class="flex mt-auto pt-4 space-x-3">
                        @notsubscriber
                            <a href="{{ route('settings.subscription') }}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <x-phosphor-sparkle-duotone class="w-5 h-5 mr-2" />
                                Abonează-te
                            </a>
                        @endnotsubscriber
                        <a href="{{ route('buy-credits') }}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-zinc-700 bg-white border border-zinc-300 rounded-md shadow-sm hover:bg-zinc-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-zinc-700 dark:text-zinc-200 dark:border-zinc-600 dark:hover:bg-zinc-600">
                            <x-phosphor-coins-duotone class="w-5 h-5 mr-2" />
                            Cumpără Credite
                        </a>
                    </div>
                </div>
            </div>
		



        <div class="mt-5 space-y-5">
            @subscriber
                <p>Ai un cont cu abonament activ </strong>.</p>
                <x-app.message-for-subscriber />
            @else
                <p>Contul tău nu are un abonament activ. Pentru a beneficia de funcționalități premium, <a href="{{ route('settings.subscription') }}" class="underline">abonează-te la un plan</a>. </p>
            @endsubscriber
            
          
        </div>
    </x-app.container>
</x-layouts.app>
