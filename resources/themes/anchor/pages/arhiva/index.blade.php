<?php
    use function Laravel\Folio\{middleware, name};
    use Livewire\Volt\Component;
    use App\Models\DocumentCategory;
    use Illuminate\Support\Facades\Auth;
    
    middleware('auth');
    name('arhiva');
    
    new class extends Component {
        public $categories;
        
        public function mount() {
            $this->categories = DocumentCategory::withCount(['documents' => function($query) {
                $query->where('user_id', Auth::id());
            }])->get();
        }
    };
?>

<x-layouts.app>
    <x-app.container>
        <div class="flex flex-col space-y-6">
            <div>
                <h1 class="text-2xl font-bold text-zinc-900 dark:text-white">A<PERSON><PERSON> de documente</h1>
                <p class="text-sm text-zinc-500 dark:text-zinc-400">Toate documentele tale generate</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                @foreach($categories as $category)
                    <a href="{{ url('arhiva/' . $category->slug) }}" 
                       class="flex flex-col p-6 bg-white dark:bg-zinc-800 rounded-xl shadow-sm border border-zinc-200 dark:border-zinc-700 hover:border-primary-500 dark:hover:border-primary-500 transition-colors">
                        <div class="flex items-center mb-4">
                            <x-phosphor-folder-open class="w-8 h-8 text-primary-500 mr-3" />
                            <h2 class="text-lg font-medium text-zinc-900 dark:text-white">{{ $category->name }}</h2>
                        </div>
                        <p class="text-sm text-zinc-500 dark:text-zinc-400 mb-4">
                            Ai {{ $category->documents_count }} documente în această categorie.
                        </p>
                        <div class="mt-auto flex justify-end">
                            <span class="text-primary-500 text-sm font-medium flex items-center">
                                Vezi documentele
                                <x-phosphor-arrow-right class="w-4 h-4 ml-1" />
                            </span>
                        </div>
                    </a>
                @endforeach
            </div>
        </div>
    </x-app.container>
</x-layouts.app>
