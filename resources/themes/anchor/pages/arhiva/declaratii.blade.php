
<?php
    use function Laravel\Folio\{middleware, name};
    use Livewire\Volt\Component;
    use App\Models\DocumentCategory;
    use App\Models\Document;
    use App\Models\Template;
    use Illuminate\Support\Facades\Auth;
    use Illuminate\Support\Facades\Session;
    
    middleware('auth');
    name('arhiva.declaratii');
    
    new class extends Component {
        public $category;
        public $documents;
        public $userDraft;
        public $step;
        public $templateName;
        public $creationDate;
        
        public function mount() {
            $this->category = DocumentCategory::where('slug', 'declaratii')->firstOrFail();
            $this->documents = Document::where('user_id', Auth::id())
                ->where('category_id', $this->category->id)
                ->orderBy('created_at', 'desc')
                ->get();
                
            // Check for draft using session data instead of a new query
            $this->userDraft = Session::has('has_draft') ? Session::get('has_draft') : false;
            $this->step = Session::get('draft_step', 2);
            
            // Get draft details from session if available
            if ($this->userDraft && Session::has('draft_template_name') && Session::has('draft_creation_date')) {
                $this->templateName = Session::get('draft_template_name');
                $this->creationDate = Session::get('draft_creation_date');
            }
        }
    };
?>

<x-layouts.app>
    @volt('arhiva.declaratii')
    <x-app.container>
        <div class="flex flex-col space-y-6">
            <!-- Header -->
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-zinc-900 dark:text-white">{{ $category->name }}</h1>
                    <p class="text-sm text-zinc-500 dark:text-zinc-400">Documentele tale din categoria {{ $category->name }}</p>
                </div>
                <a href="{{ route('dashboard') }}" class="btn btn-outline btn-sm">
                    <x-phosphor-arrow-left class="w-4 h-4 mr-1" />
                    Înapoi
                </a>
            </div>

            <!-- Documents Table -->
            <div class="overflow-x-auto bg-white dark:bg-zinc-800 rounded-xl shadow-sm border border-zinc-200 dark:border-zinc-700">
                @if($documents->count() > 0)
                    <table class="table w-full table-zebra dark:table-dark dark:border-zinc-900">
                        <thead>
                            <tr class="bg-zinc-50 dark:bg-zinc-700/50 text-zinc-500 dark:text-zinc-400">
                                <th class="text-left pl-6">#</th>
                                <th class="text-left">Previzualizare</th>
                                <th class="text-left">Nume document</th>
                                <th class="text-left">Data creării</th>
                                <th class="text-right pr-6">Acțiuni</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($documents as $index => $document)
                                <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700/30">
                                    <td class="pl-6">{{ $index + 1 }}</td>
                                    <td>
                                        <div class="w-16 h-20 bg-zinc-100 dark:bg-zinc-700 rounded overflow-hidden">
                                            @if($document->thumbnail)
                                                <img src="{{ $document->thumbnail }}" alt="Preview" class="w-full h-full object-cover">
                                            @else
                                                <div class="w-full h-full flex items-center justify-center">
                                                    <x-phosphor-file-text class="w-8 h-8 text-zinc-400" />
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="font-medium text-zinc-900 dark:text-white">
                                            {{ $document->template->name ?? 'Document' }}
                                        </div>
                                        <div class="text-xs text-zinc-500">
                                            {{ $document->document_number }}
                                        </div>
                                    </td>
                                    <td>{{ $document->created_at->format('d.m.Y, H:i') }}</td>
                                    <td class="text-right pr-6">
                                        <div class="flex justify-end space-x-2">
                                             @if($document->template->tier_id <= 1 || $document->was_paid_for)
                                                <a href="{{ route('documents.download', $document->id) }}" class="btn btn-sm btn-primary">
                                                    <x-phosphor-download class="w-4 h-4 mr-1" />
                                                    Descarcă
                                                </a>
                                            @else
                                                <a href="{{ route('documents.payment', $document->id) }}" class="btn btn-sm btn-secondary">
                                                    <x-phosphor-download class="w-4 h-4 mr-1" />
                                                    Plătește & Descarcă
                                                </a>
                                            @endif
                                            
                                            <form action="{{ route('documents.delete', $document->id) }}" method="POST" onsubmit="return confirm('Ești sigur că vrei să ștergi acest document?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-error">
                                                    <x-phosphor-trash class="w-4 h-4 mr-1" />
                                                    Șterge
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    <div class="flex flex-col items-center justify-center py-12 px-4 text-center">
                        <x-phosphor-folder-open class="w-16 h-16 text-zinc-300 dark:text-zinc-600 mb-4" />
                        <h3 class="text-lg font-medium text-zinc-900 dark:text-white mb-1">Nu ai documente în această categorie</h3>
                        <p class="text-zinc-500 dark:text-zinc-400 mb-6 max-w-md">
                            Nu ai generat încă niciun document în categoria {{ $category->name }}. Poți crea un document nou folosind butonul de mai jos.
                        </p>
                        
                        <button type="button" class="btn btn-primary btn-md w-full text-base shadow-xl"
                            @if($userDraft)
                                onclick="document_draft_prompt.showModal()"
                            @else
                                onclick="window.location.href='/editor'"
                            @endif
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="#000000" class="w-6 h-6 mr-2" viewBox="0 0 256 256">
                                <path d="M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34ZM160,51.31,188.69,80H160ZM200,216H56V40h88V88a8,8,0,0,0,8,8h48V216Zm-40-64a8,8,0,0,1-8,8H136v16a8,8,0,0,1-16,0V160H104a8,8,0,0,1,0-16h16V128a8,8,0,0,1,16,0v16h16A8,8,0,0,1,160,152Z">
                                </path>
                            </svg>
                            Document nou
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </x-app.container>
  
    @endvolt
</x-layouts.app>

