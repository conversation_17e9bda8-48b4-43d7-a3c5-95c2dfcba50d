<?php

    use function Laravel\Folio\{middleware, name};
    use Livewire\Volt\Component;
    use Livewire\Attributes\Computed;
    name('wave.profile');

    new class extends Component
    {
        public $username;

        #[Computed]
        public function user()
        {
            return config('wave.user_model')::where('username', '=', $this->username)->with('roles')->firstOrFail();
        }
    }
?>

<x-layouts.app>
    @volt('wave.profile')
        <x-app.container>
            @guest
                <x-marketing.elements.heading
                    level="h2"
                    class="mt-5"
                    :title="$this->user->name"
                    :description="'Currently viewing ' . $this->user->username . '\'s profile'" 
                />
            @endguest

            <div class="lg:w-2/3 space-y-5">
                @auth
                    <livewire:credit-balance />
                @endauth
    
                <x-card class="lg:p-10 lg:text-left text-center">
                    <p class="text-sm text-zinc-600 dark:text-zinc-400">This is the application user profile page.</p>
                    <p class="mt-2 text-sm text-zinc-600 dark:text-zinc-400">You can modify this file from your template <strong>resources/themes/anchor</strong> at:</p>
                    <code class="inline-block px-2 py-1 mt-2 font-mono text-sm font-medium bg-gray-100 dark:bg-zinc-800 rounded-md text-zinc-600 dark:text-zinc-400">{{ 'pages/profile/[username].blade.php' }}</code>
                </x-card>
            </div>
        </x-app.container>
    @endvolt
</x-layouts.app>
