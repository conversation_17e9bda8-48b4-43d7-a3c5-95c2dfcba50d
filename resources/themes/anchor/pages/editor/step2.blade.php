<?php
    use Livewire\Volt\Component;
    use App\Models\DocumentDraft;
    use function Laravel\Folio\{middleware, name};
    
    middleware(['auth', 'editor.step']);
    name('editor.step2');

    new class extends Component {

         #[Rule('required')] 
        public $hasDraft;
   
        public $nextEnabled = false;
        public $step = 2;

        protected $listeners = ['enableNext'];

        public function enableNext()
        {
            $this->nextEnabled = true;
        }

        public function step3()
        {
            $draft = DocumentDraft::where('user_id', auth()->id())->first();
            if ($draft) {
                $draft->current_step = 3;
                $draft->save();
            }
            
            session(['step' => 3]);
            return redirect('/editor/step3');
        }
    


    } 
    
   

?>

<x-layouts.app>
  {{-- Wrap everything in a single Livewire component to maintain state --}}
  @volt('step2')
  <div
      x-data="{ 
          initialized: false,
          init() {
              if (this.initialized) return;
              this.initialized = true;

              // Wait for Livewire to be ready
              document.addEventListener('livewire:initialized', () => {
                  // Then wait for Alpine
                  window.Alpine.nextTick(() => {
                      // Now safe to initialize any additional functionality
                      this.$wire.dispatch('component-ready');
                  });
              });

              // Handle cleanup
              document.addEventListener('livewire:navigating', () => {
                  this.initialized = false;
              });
          }
      }"
      x-cloak
      wire:key="step2-container"
  >
   
   <x-app.container class="lg:space-y-6">
    <div class="w-full flex flex-row justify-between items-center">
        <div class="pb-5 border-b border-gray-200 dark:border-gray-800 space-y-0.5">
            <h3 class="text-lg sm:text-xl font-semibold tracking-tight dark:text-zinc-100">
                Introdu datele părților implicate
            </h3>
            <p class="text-xs sm:text-sm text-zinc-500 dark:text-zinc-400 w-2/3">
                Dacă ai început o declarație sau o cerere, aici introduci doar datele tale.
                Dacă ai început un contract, introdu datele tale și ale celeilalte părți.
            </p>
        </div>

        <div class="flex flex-row justify-right items-center gap-2 space-x-4">
            <button 
                type="button" 
                class="btn btn-primary btn-sm text-base shadow-xl"
                wire:click="step3"
                @if(!$nextEnabled) disabled @endif
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" class="px-0" viewBox="0 0 256 256">
                    <path d="M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z">
                    </path>
                </svg>
            </button>
        </div>
    </div>

  {{-- Wrap nested components in a container that only renders when parent is ready --}}
  <div 
  class="w-full flex flex-col justify-center items-center"
  x-show="initialized"
  x-cloak
>
  {{-- Add wire:key to help Livewire track components --}}
  <div wire:key="step02-component">
      <livewire:step02 :key="'step02-'.now()" />
  </div>
  <div wire:key="steps-display-component">
      <livewire:steps-display :key="'steps-display-'.now()" />
  </div>
</div>
</div>
</x-app.container>
    @endvolt
</x-layouts.app>
