<?php
    use function Laravel\Folio\{middleware, name};

    use Livewire\Volt\Component;
    use Livewire\Attributes\Rule;
    use Livewire\Attributes\Computed;
    use App\Models\DocumentDraft;
    
    middleware(['auth', 'editor.step']);
    name('editor');

    new class extends Component {

         #[Rule('required')] 
        public $hasDraft;
   
        public $nextEnabled = false;
        public $step = 1;
        public $templateId;

        protected $listeners = ['enableNextStep'];

        public function enableNextStep($payload)
        {
            $this->nextEnabled = true;
            $this->templateId = $payload['templateId'];
        }

        public function step2()
        {
            $draft = DocumentDraft::where('user_id', auth()->id())->first();
            if ($draft) {
                $draft->current_step = 2;
                $draft->save();
            } else {
                $draft = DocumentDraft::create([
                    'user_id' => auth()->id(),
                    'current_step' => 2,
                    'template_id' => $this->templateId
                ]);
            }
            
            session(['step' => 2]);
            return redirect('/editor/step2');
        }


    } 

?>

<x-layouts.app>
   @volt('editor')
	<x-app.container x-data class="lg:space-y-6" x-cloak>
            <div class="w-full flex flex-row justify-between items-center">
                    <div class= "pb-5 border-b border-gray-200 dark:border-gray-800 space-y-0.5">

                        <h3 class="text-lg sm:text-xl font-semibold tracking-tight dark:text-zinc-100">Ce document generăm azi?</h3>
                        <p class="text-xs sm:text-sm text-zinc-500 dark:text-zinc-400">Alege aici tipul și categoria de document de care ai nevoie</p>

                    </div>


            

                    <div class="flex flex-row justify-right items-center gap-2 space-x-4">
                                <a href="{{ route('editor') }}" class="btn btn-sm btn-neutral shadow-xl">Resetează selecțiile</a>
                                <button type="button" class="btn btn-primary btn-sm text-base shadow-xl"
                                wire:click.prevent="step2"
                               
                                {{ $nextEnabled ? '' : 'disabled' }}
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" class="px-0" viewBox="0 0 256 256">
                                <path d="M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z">
                                </path>
                                </svg>
                                <!-- Continuă -->
                                </button>
        
                    </div>

            </div>
    <div class="w-full flex flex-col justify-center items-center">

   <livewire:step01 />
  


    <livewire:steps-display />
    </div>
</x-app.container>
    @endvolt
</x-layouts.app>
