<?php
    
    use App\Models\DocumentCategory;
    use function Laravel\Folio\{middleware, name};
    use Livewire\Volt\Component;
    use Livewire\Attributes\Rule;
    use Livewire\Attributes\Computed;
    use App\Models\DocumentDraft;

	middleware(['auth', 'editor.step']);
    name('editor.step4');

    new class extends Component {

         #[Rule('required')] 
        public $hasDraft;
   
        public $nextEnabled = false;
        public $step = 4;

        protected $listeners = ['enableNext'];

        public function enableNext($hasDraft)
        {
            $this->nextEnabled = true;
        }

        public function step1()
        {
            $draft = DocumentDraft::where('user_id', auth()->id())->first();
            if ($draft) {
                $draft->current_step = 1;
                $draft->save();
            }
            
            session(['step' => 1]);
            return redirect('/editor'); // Changed from /editor/step1 to /editor
        }
    


    } 
    
   

?>

<x-layouts.app>
   @volt('step4')
	<x-app.container x-data class="lg:space-y-6" x-cloak>
            <div class="w-full flex flex-row justify-between items-center">
                    <div class= "pb-5 border-b border-gray-200 dark:border-gray-800 space-y-0.5">

                        <h3 class="text-lg sm:text-xl font-semibold tracking-tight dark:text-zinc-100">Descarcă documentul</h3>
                        <p class="text-xs sm:text-sm text-zinc-500 dark:text-zinc-400 w-2/3">În sfârșit! Ultimul pas, intri în posesia documentului.
                        </p>

                    </div>


            

                    <div class="flex flex-row justify-right items-center gap-2 space-x-4">
                            <!-- 
                                <button type="button" class="btn btn-primary btn-wide text-base shadow-xl"
                                wire:click.prevent="step3"
                               
                                {{ $nextEnabled ? '' : 'disabled' }}
                                >
                                Continuă
                                </button>
                              -->
                    </div>

            </div>
    <div class="w-full flex flex-col justify-center items-center">

   <livewire:step04 />
  


    <livewire:steps-display />
    </div>
</x-app.container>
    @endvolt
</x-layouts.app>
