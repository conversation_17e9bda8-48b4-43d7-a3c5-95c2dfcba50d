<?php
    use App\Models\DocumentDraft;
    use function Laravel\Folio\{middleware, name};
    use Livewire\Volt\Component;
    
    middleware(['auth', 'editor.step']);
    name('editor.step3');

    new class extends Component {

        #[Rule('required')] 

        public $nextEnabled = false;
        public $step = 3;

        protected $listeners = ['enableNext'];

        public function enableNext()
        {
            $this->nextEnabled = true;
        }

        public function step4()
        {
            $draft = DocumentDraft::where('user_id', auth()->id())->first();
            if ($draft) {
                $draft->current_step = 4;
                $draft->save();
            }
            
            session(['step' => 4]);
            $this->dispatch('finalizeDocument');
        }
    }
    
   

?>

<x-layouts.app>
   @volt('step3')
	<x-app.container x-data class="lg:space-y-6" x-cloak>
            <div class="w-full flex flex-row justify-between items-center">
                    <div class= "pb-5 border-b border-gray-200 dark:border-gray-800 space-y-0.5">

                        <h3 class="text-lg sm:text-xl font-semibold tracking-tight dark:text-zinc-100">Introdu detaliile documentului</h3>
                        <p class="text-xs sm:text-sm text-zinc-500 dark:text-zinc-400 w-2/3">Completează cu detaliile specifice ale documentului tău.
                        Acum e momentul să configurezi prețuri, date, termene și să adaptezi documentul la nevoile tale.
                        </p>

                    </div>


            

                    <div class="flex flex-row justify-right items-center gap-2 space-x-4">
    
                                <button type="button" class="btn btn-primary btn-sm text-base shadow-xl"
                                wire:click.prevent="step4"
                               
                                {{ $nextEnabled ? '' : 'disabled' }}
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" class="px-0" viewBox="0 0 256 256">
                                <path d="M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z">
                                </path>
                                </svg>
                                <!-- Continuă -->
                                </button>
        
                    </div>

            </div>
    <div class="w-full flex flex-col justify-center items-center">

   <livewire:step03 />
  


    <livewire:steps-display />
    </div>
</x-app.container>
    @endvolt
</x-layouts.app>
