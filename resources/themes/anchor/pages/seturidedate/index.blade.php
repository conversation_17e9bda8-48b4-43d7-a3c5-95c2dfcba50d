<?php
    use App\Models\InfoProfile;
    use function Laravel\Folio\{middleware, name};
    use Livewire\Volt\Component;
    
    middleware('auth');
    name('seturidedate');
    
    new class extends Component {
        public $infoprofiles;
        public $showDeleteModal = false;
        public $profileToDelete = null;
        public $showProfileModal = false;
        public $editProfileId = null;
        
        public function mount() {
            $this->loadProfiles();
        }
        
        public function loadProfiles() {
            $this->infoprofiles = InfoProfile::where('user_id', auth()->id())->get();
        }
        
        public function confirmDelete($id) {
            $this->profileToDelete = $id;
            $this->showDeleteModal = true;
        }
        
        public function deleteProfile() {
            if ($this->profileToDelete) {
                InfoProfile::where('id', $this->profileToDelete)
                    ->where('user_id', auth()->id())
                    ->delete();
                
                $this->showDeleteModal = false;
                $this->profileToDelete = null;
                $this->loadProfiles();
                
                session()->flash('message', 'Setul de date a fost șters cu succes.');
            }
        }
        
        public function cancelDelete() {
            $this->showDeleteModal = false;
            $this->profileToDelete = null;
        }
        
        public function openAddModal() {
            $this->editProfileId = null;
            $this->showProfileModal = true;
        }
        
        public function openEditModal($id) {
            $this->editProfileId = $id;
            $this->showProfileModal = true;
        }
        
        #[\Livewire\Attributes\On('profileSaved')]
        public function handleProfileSaved() {
            $this->showProfileModal = false;
            $this->loadProfiles();
            session()->flash('message', 'Setul de date a fost salvat cu succes.');
        }
    };
?>

<x-layouts.app>
    @volt('seturidedate')
    <x-app.container x-data class="lg:space-y-6" x-cloak>
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-zinc-900 dark:text-white">Seturi de date</h1>
                <p class="text-sm text-zinc-500 dark:text-zinc-400">Gestionează datele tale personale și ale companiilor</p>
            </div>
            <a href="{{ route('dashboard') }}" class="btn btn-outline btn-sm">
                <x-phosphor-arrow-left class="w-4 h-4 mr-1" />
                Înapoi
            </a>
        </div>
        
        <!-- Success Message -->
        @if(session()->has('message'))
            <div class="alert alert-success mb-6">
                <x-phosphor-check-circle class="w-5 h-5" />
                <span>{{ session('message') }}</span>
            </div>
        @endif
        
        <!-- Add New Profile Button -->
        <div class="mb-6">
            <button wire:click="openAddModal" class="btn btn-primary">
                <x-phosphor-plus class="w-5 h-5 mr-1" />
                Adaugă set nou de date
            </button>
        </div>
        
        <!-- Profiles Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @forelse($infoprofiles as $profile)
                <div class="flex flex-col bg-white dark:bg-zinc-800 rounded-xl shadow-sm border border-zinc-200 dark:border-zinc-700 overflow-hidden h-full">
                    <div class="p-5 flex-grow">
                        <div class="flex items-center mb-3">
                            @if($profile->entity_type == 'company')
                                <x-phosphor-buildings class="w-6 h-6 text-primary-500 mr-2" />
                                <h3 class="text-lg font-medium text-zinc-900 dark:text-white">{{ $profile->company_name }}</h3>
                            @else
                                <x-phosphor-user class="w-6 h-6 text-primary-500 mr-2" />
                                <h3 class="text-lg font-medium text-zinc-900 dark:text-white">{{ $profile->pronoun }} {{ $profile->first_name }} {{ $profile->last_name }}</h3>
                            @endif
                        </div>
                        
                        <div class="space-y-2 text-sm text-zinc-600 dark:text-zinc-400">
                            @if($profile->entity_type == 'company')
                                <p><span class="font-medium">CUI:</span> {{ $profile->tax_identification_number }}</p>
                                <p><span class="font-medium">Reg. Com.:</span> J{{ $profile->registration_number }}</p>
                            @else
                                <p><span class="font-medium">CNP:</span> {{ $profile->personal_id_number }}</p>
                                <p><span class="font-medium">Act identitate:</span> {{ $profile->id_document_type }} {{ $profile->id_document_series }} {{ $profile->id_document_number }}</p>
                            @endif
                            <p><span class="font-medium">Email:</span> {{ $profile->email }}</p>
                            <p><span class="font-medium">Telefon:</span> {{ $profile->phone }}</p>
                        </div>
                    </div>
                    
                    <div class="bg-zinc-50 dark:bg-zinc-700/30 p-3 flex justify-end space-x-2 border-t border-zinc-200 dark:border-zinc-700 mt-auto">
                        <button class="btn btn-sm btn-ghost" wire:click="openEditModal({{ $profile->id }})">
                            <x-phosphor-pencil class="w-4 h-4 mr-1" />
                            Editează
                        </button>
                        <button class="btn btn-sm btn-error" wire:click="confirmDelete({{ $profile->id }})">
                            <x-phosphor-trash class="w-4 h-4 mr-1" />
                            Șterge
                        </button>
                    </div>
                </div>
            @empty
                <div class="col-span-full flex flex-col items-center justify-center py-12 px-4 text-center bg-white dark:bg-zinc-800 rounded-xl shadow-sm border border-zinc-200 dark:border-zinc-700">
                    <x-phosphor-user-circle class="w-16 h-16 text-zinc-300 dark:text-zinc-600 mb-4" />
                    <h3 class="text-lg font-medium text-zinc-900 dark:text-white mb-1">Nu ai seturi de date</h3>
                    <p class="text-zinc-500 dark:text-zinc-400 mb-6 max-w-md">
                        Nu ai adăugat încă niciun set de date. Poți adăuga un set nou folosind butonul de mai jos.
                    </p>
                    <button wire:click="openAddModal" class="btn btn-primary">
                        <x-phosphor-plus class="w-5 h-5 mr-1" />
                        Adaugă set nou de date
                    </button>
                </div>
            @endforelse
        </div>
        
        <!-- Delete Confirmation Modal -->
        <div x-show="$wire.showDeleteModal" class="fixed inset-0 z-50 overflow-y-auto" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                    <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
                </div>
                
                <div class="inline-block align-bottom bg-white dark:bg-zinc-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                    <div class="bg-white dark:bg-zinc-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10">
                                <x-phosphor-warning-circle class="h-6 w-6 text-red-600 dark:text-red-400" />
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-zinc-900 dark:text-white">
                                    Șterge setul de date
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-zinc-500 dark:text-zinc-400">
                                        Ești sigur că vrei să ștergi acest set de date? Această acțiune nu poate fi anulată.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-zinc-50 dark:bg-zinc-700/30 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button wire:click="deleteProfile" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Șterge
                        </button>
                        <button wire:click="cancelDelete" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-zinc-300 dark:border-zinc-600 shadow-sm px-4 py-2 bg-white dark:bg-zinc-800 text-base font-medium text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Anulează
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Profile Modal -->
        @if($showProfileModal)
            <div class="fixed inset-0 z-50 overflow-y-auto">
                <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                    <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
                    </div>
                    
                    <div class="inline-block align-bottom bg-white dark:bg-zinc-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                        <div class="bg-white dark:bg-zinc-800 px-4 pt-5 pb-4 sm:p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg leading-6 font-medium text-zinc-900 dark:text-white">
                                    {{ $editProfileId ? 'Editează setul de date' : 'Adaugă set nou de date' }}
                                </h3>
                                <button wire:click="$set('showProfileModal', false)" class="text-zinc-400 hover:text-zinc-500">
                                    <x-phosphor-x class="h-6 w-6" />
                                </button>
                            </div>
                            
                            @if($editProfileId)
                                <livewire:info-profile-editor :profileId="$editProfileId" :key="'profile-editor-'.$editProfileId" />
                            @else
                                <livewire:info-profile-manager :key="'profile-manager-'.now()" />
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </x-app.container>
    @endvolt
</x-layouts.app>
