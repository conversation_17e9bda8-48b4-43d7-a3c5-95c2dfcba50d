<?php
    use function Laravel\Folio\{middleware, name};
    middleware('auth');
    name('buy-credits');
?>

<x-layouts.app>
    <x-app.container x-data="{
        payAsYouGo: {
            credits: 5,
            pricePerUnit: 50,
            calculateTotal() {
                return (Math.ceil(this.credits / 5) * this.pricePerUnit).toFixed(2);
            }
        }
    }" class="space-y-6" x-cloak>
        <div class="w-full">
            <x-app.heading
                title="Purchase Credits"
                description="Choose between pay-as-you-go credits or subscription packages"
            />

            <div class="mt-8 space-y-8">
                <!-- Pay As You Go Section -->
                <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-zinc-800 ring-1 ring-zinc-900/5 dark:ring-white/10 mb-4">
                    <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">
                        Pay As You Go
                    </h3>
                    <p class="mt-2 text-sm text-zinc-500 dark:text-zinc-400">
                        Purchase credits in multiples of 5
                    </p>
                    
                    <div class="mt-6 space-y-4">
                        <div>
                            <label class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                Select number of credits
                            </label>
                            <input 
                                type="range" 
                                x-model="payAsYouGo.credits"
                                min="5" 
                                max="100" 
                                step="5"
                                class="w-full h-2 mt-2 bg-zinc-200 rounded-lg appearance-none cursor-pointer dark:bg-zinc-700"
                            >
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-zinc-600 dark:text-zinc-400">
                                <span x-text="payAsYouGo.credits"></span> credits
                            </span>
                            <span class="text-lg font-medium text-zinc-900 dark:text-zinc-100">
                                Lei <span x-text="payAsYouGo.calculateTotal()"></span>
                            </span>
                        </div>
                        
                        <button class="w-full btn btn-primary">
                            Purchase Credits
                        </button>
                    </div>
                </div>

                <!-- Subscription Packages -->
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <!-- Basic Subscription -->
                    <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-zinc-800 ring-1 ring-zinc-900/5 dark:ring-white/10">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">
                                Basic Plan
                            </h3>
                            <span class="px-2.5 py-0.5 text-xs font-medium text-primary-700 bg-primary-50 rounded-full dark:bg-primary-900/50 dark:text-primary-400">
                                Most Popular
                            </span>
                        </div>
                        
                        <div class="mt-4 space-y-4">
                            <div class="flex items-baseline">
                                <span class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">Lei 35</span>
                                <span class="ml-1 text-sm text-zinc-500 dark:text-zinc-400">/month</span>
                            </div>
                            <p class="text-sm text-zinc-500 dark:text-zinc-400">
                                5 credits per month
                            </p>
                            <ul class="space-y-2">
                                <li class="flex items-center text-sm text-zinc-500 dark:text-zinc-400">
                                    <svg class="w-4 h-4 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Lei 260/year (60 credits)
                                </li>
                                <li class="flex items-center text-sm text-zinc-500 dark:text-zinc-400">
                                    <svg class="w-4 h-4 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Save with annual billing
                                </li>
                            </ul>
                            <button class="w-full btn btn-primary">
                                Subscribe Monthly
                            </button>
                            <button class="w-full btn btn-secondary">
                                Subscribe Yearly
                            </button>
                        </div>
                    </div>

                    <!-- Premium Subscription -->
                    <div class="p-6 bg-white rounded-lg shadow-sm dark:bg-zinc-800 ring-1 ring-zinc-900/5 dark:ring-white/10">
                        <h3 class="text-lg font-medium text-zinc-900 dark:text-zinc-100">
                            Premium Plan
                        </h3>
                        
                        <div class="mt-4 space-y-4">
                            <div class="flex items-baseline">
                                <span class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">Lei 50</span>
                                <span class="ml-1 text-sm text-zinc-500 dark:text-zinc-400">/month</span>
                            </div>
                            <p class="text-sm text-zinc-500 dark:text-zinc-400">
                                10 credits per month
                            </p>
                            <ul class="space-y-2">
                                <li class="flex items-center text-sm text-zinc-500 dark:text-zinc-400">
                                    <svg class="w-4 h-4 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Lei 330/year (120 credits)
                                </li>
                                <li class="flex items-center text-sm text-zinc-500 dark:text-zinc-400">
                                    <svg class="w-4 h-4 mr-2 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Best value for power users
                                </li>
                            </ul>
                            <button class="w-full btn btn-primary">
                                Subscribe Monthly
                            </button>
                            <button class="w-full btn btn-secondary">
                                Subscribe Yearly
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8">
                <livewire:credit-purchase-history />
            </div>
        </div>
    </x-app.container>
</x-layouts.app>
