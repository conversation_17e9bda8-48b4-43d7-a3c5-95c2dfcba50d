@props([
    'buttonAction' => 'nextStep()', // Default action
    'buttonLabel' => 'Next Step',   // Optional: Button label
    'buttonClasses' => 'bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded' // Optional: Button classes
])
<div 
    x-data="{ fullscreenModal: false }"
    x-init="
    $watch('fullscreenModal', function(value){
            if(value === true){
                document.body.classList.add('overflow-hidden');
            }else{
                document.body.classList.remove('overflow-hidden');
            }
        })
    "
    @keydown.escape="fullscreenModal=false"
    >
    
   
    <x-button wire:key="{{ Str::random(10) }}"  @click="fullscreenModal=true" class="w-64 bg-primary-50 text-base shadow-xl" 
    icon="phosphor-address-book"  >
    {{ $buttonLabel }}</x-button>       
    <template x-teleport="body">

        <div 
            x-show="fullscreenModal"
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-100"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            class="flex fixed inset-0 z-[99] w-screen h-screen backdrop-blur-lg bg-black/50"
            >
            <button wire:key="{{ Str::random(10) }}"  @click="fullscreenModal=false" class="absolute top-0 right-0 z-30 flex items-center justify-center px-3 py-2 mt-3 mr-3 space-x-1 text-xs font-medium uppercase border rounded-md border-neutral-200 text-neutral-600 hover:bg-neutral-100">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
                <span>Close</span>
            </button>
            <div class="relative top-0 bottom-0 right-0 flex-shrink-0 hidden w-1/3 overflow-hidden  lg:block">
              </div>
            <div class="relative flex flex-wrap items-center w-full h-full bg-zinc-100 dark:bg-zinc-900 px-8">
                <template x-if="fullscreenModal">
                  {{$slot}}
                </template>
              
            </div>
        </div>
        
    </template>
</div>