@php
    
use Illuminate\View\View;
use App\Models\DocumentCategory;
use App\Models\InfoProfile;
use App\Models\DocumentDraft;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Volt\Component;

use function Laravel\Folio\{middleware, name};
//use function Livewire\Volt\{action};

$user = Auth::user();

// Retrieve all DocumentCategories with the count of documents owned by the user
$categories = DocumentCategory::withCount(['documents' => function($query) use ($user) {
    $query->where('user_id', $user->id);
}])->get();

// Retrieve InfoProfiles where is_temp is false
$infoprofiles = InfoProfile::where('is_temp', false)->withCount(['user' => function($query) use ($user) {
    $query->where('user_id', $user->id);
}])->get();

$draftExists = DocumentDraft::where('user_id', $user->id)->exists();
$lastDraft = DocumentDraft::where('user_id', $user->id)->latest()->first();
$userDraft = DocumentDraft::where('user_id', auth()->id())->exists();
$draftObject = DocumentDraft::where('user_id', auth()->id())->first();
$step = $draftObject->current_step ?? 2;

Log::info($userDraft . "draft at step" .  $step );

@endphp

<div x-data="{ sidebarOpen: false }"  @open-sidebar.window="sidebarOpen = true"
    x-init="
        $watch('sidebarOpen', function(value){
            if(value){ document.body.classList.add('overflow-hidden'); } else { document.body.classList.remove('overflow-hidden'); }
        });
    "
    class="relative z-50 w-screen md:w-auto" x-cloak>
    {{-- Backdrop for mobile --}}
    <div x-show="sidebarOpen" @click="sidebarOpen=false" class="fixed top-0 right-0 z-50 w-screen h-screen duration-300 ease-out bg-black/20 dark:bg-white/10"></div>
    
    {{-- Sidebar --}} 
    <div :class="{ '-translate-x-full': !sidebarOpen }"
        class="fixed top-0 left-0 flex items-stretch -translate-x-full overflow-hidden lg:translate-x-0 z-50 h-dvh md:h-screen transition-[width,transform] duration-150 ease-out bg-zinc-50 dark:bg-zinc-900 w-64 group ">  
        <div class="flex flex-col justify-between w-full overflow-auto md:h-full h-svh pt-4 pb-2.5">
            <div class="relative flex flex-col">
                <button x-on:click="sidebarOpen=false" class="flex items-center justify-center flex-shrink-0 w-10 h-10 ml-4 rounded-md lg:hidden text-zinc-400 hover:text-zinc-800 dark:hover:text-zinc-200 dark:hover:bg-zinc-700/70 hover:bg-gray-200/70">
                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" /></svg>
                </button>

                <div class="flex items-center px-5 space-x-2">
                    <a href="/" class="flex justify-center items-center py-4 pl-0.5 space-x-1 font-bold text-zinc-900">
                        <x-logo class="w-auto h-7" />
                    </a>
                </div>
                <div class="flex items-center px-4 pt-1 pb-3 mt-4">
                    <div class="relative flex items-center w-full h-full rounded-lg">
                        <x-phosphor-magnifying-glass class="absolute left-0 w-5 h-5 ml-2 text-gray-400 -translate-y-px" />
                        <input type="text" class="w-full py-2 pl-8 text-sm border rounded-lg bg-zinc-200/70 focus:bg-white duration-50 dark:bg-zinc-950 ease border-zinc-200 dark:border-zinc-700/70 dark:ring-zinc-700/70 focus:ring dark:text-zinc-200 dark:focus:ring-zinc-700/70 dark:focus:border-zinc-700 focus:ring-zinc-200 focus:border-zinc-300 dark:placeholder-zinc-400" placeholder="Search">
                    </div>
                </div>

                <div class="flex flex-col gap-y-2 justify-start items-center px-4 space-y-1.5 w-full h-full text-slate-600 dark:text-zinc-400 mt-4">
                   
                   
                    <!-- Spacer with larger margin -->
                 
                    <x-app.sidebar-link href="/dashboard"  icon="phosphor-house" :active="Request::is('dashboard')" data-tip="hello">Panou de control</x-app.sidebar-link>
                  
                  <!-- including new document button code directly in sidebar as test -->
                  <button type="button" class="btn btn-primary btn-md w-full text-base shadow-xl "
                  @if($userDraft)
                  onclick="document_draft_prompt.showModal()"
              @else
                  onclick="window.location.href='/editor'"
              @endif
                  >
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="#000000"  class="w-6 h-6 mr-2" viewBox="0 0 256 256">
                  <path d="M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34ZM160,51.31,188.69,80H160ZM200,216H56V40h88V88a8,8,0,0,0,8,8h48V216Zm-40-64a8,8,0,0,1-8,8H136v16a8,8,0,0,1-16,0V160H104a8,8,0,0,1,0-16h16V128a8,8,0,0,1,16,0v16h16A8,8,0,0,1,160,152Z">
                  </path></svg>
                  Document nou
                  </button>  

                    <x-app.sidebar-dropdown text="Arhiva mea" icon="phosphor-stack" id="projects_dropdown" :active="(Request::is('documents'))" :open="(Request::is('project_a') || Request::is('project_b') || Request::is('project_c')) ? '1' : '0'">
                       {{--  <x-app.sidebar-link  icon="phosphor-cube" :active="(Request::is('project_a'))">Project A</x-app.sidebar-link>
                        <x-app.sidebar-link  icon="phosphor-cube" :active="(Request::is('project_b'))">Project B</x-app.sidebar-link>
                        <x-app.sidebar-link  icon="phosphor-cube" :active="(Request::is('project_c'))">Project C</x-app.sidebar-link> --}}

                        @foreach ($categories as $category)
                        <x-app.sidebar-link 
                            href="{{ url('arhiva/' .  $category->slug) }}"
                            icon="phosphor-folder-open" 
                            tooltip="{{ $category->name }}"   
                            :active="Request::is('arhiva/' . $category->slug)">
                            {{ $category->name }} <span class="ml-2 text-sm text-gray-500">({{ $category->documents_count }})</span>
                        </x-app.sidebar-link>
                        @endforeach
                    </x-app.sidebar-dropdown>
                                      
                    <x-app.sidebar-link href="/seturidedate" icon="phosphor-users" active="false">
                        Seturi de date <span class="ml-2 text-sm text-gray-500">({{ $infoprofiles->count() }})</span>
                    </x-app.sidebar-link>
                </div>
            </div>

            <div class="relative px-2.5 space-y-1.5 text-zinc-700 dark:text-zinc-400">
                <livewire:sidebar-credit-balance />
                
                <x-app.sidebar-link href="/help" icon="phosphor-book-bookmark-duotone" :active="Request::is('help')">Help & Documentation</x-app.sidebar-link>
                <x-app.sidebar-link href="https://devdojo.com/questions" target="_blank" icon="phosphor-chat-duotone" active="false">Questions</x-app.sidebar-link>
                <x-app.sidebar-link :href="route('changelogs')" icon="phosphor-book-open-text-duotone" :active="Request::is('changelog') || Request::is('changelog/*')">Changelog</x-app.sidebar-link>

                <div x-show="sidebarTip" x-data="{ sidebarTip: $persist(true) }" class="px-1 py-3" x-collapse x-cloak>
                    <div class="relative w-full px-4 py-3 space-y-1 border rounded-lg bg-zinc-50 text-zinc-700 dark:text-zinc-100 dark:bg-zinc-800 border-zinc-200/60 dark:border-zinc-700">
                        <button @click="sidebarTip=false" class="absolute top-0 right-0 z-50 p-1.5 mt-2.5 mr-2.5 rounded-full opacity-80 cursor-pointer hover:opacity-100 hover:bg-zinc-100 hover:dark:bg-zinc-700 hover:dark:text-zinc-300 text-zinc-500 dark:text-zinc-400">
                            <x-phosphor-x-bold class="w-3 h-3" />
                        </button>
                        <h5 class="pb-1 text-sm font-bold -translate-y-0.5">Edit This Section</h5>
                        <p class="block pb-1 text-xs opacity-80 text-balance">You can edit any aspect of your user dashboard. This section can be found inside your theme component/app/sidebar file.</p>
                    </div>
                </div>

                <div class="w-full h-px my-2 bg-slate-100 dark:bg-zinc-700"></div>
                <x-app.user-menu />

                



            </div>
        </div>
    </div>











@php
// Get template name
$templateName = '';
$creationDate = '';
if ($draftObject) {
    $template = App\Models\Template::find($draftObject->template_id);
    $templateName = $template ? $template->name : 'necunoscut';
    $creationDate = $draftObject->created_at->format('d.m.Y, H:i');
    
    // Store draft info in session
    session(['has_draft' => true, 'draft_step' => $step]);
} else {
    session()->forget(['has_draft', 'draft_step']);
}
@endphp

@if($draftObject)
<dialog id="document_draft_prompt" class="modal" >
    <div class="modal-box card bg-base-100 dark:bg-zinc-800 w-11/12 max-w-2xl" >
                    <form method="dialog" class="w-full h-6">
      <button class="btn btn-sm btn-circle btn-neutral absolute right-2 top-2">✕</button>
    </form>
        <div class="card-body" x-data="{ loading: false }">

            <h3 class="card-title text-lg font-bold text-zinc-900 dark:text-white">Ai un document început, salvat ca ciornă!</h3>
            <p class="py-4 text-zinc-700 dark:text-zinc-300">Este vorba despre un document tip {{ $templateName }}, început în {{ $creationDate }}.</p>
            <p class="text-zinc-700 dark:text-zinc-300">Dacă dorești să continui cu acest document, alege "Continuă". Dacă dorești să începi un document nou (și să pierzi informațiile nesalvate din cel existent) alege "Document nou".</p>
            <div class="modal-action w-full">
                <form method="dialog" class="w-full flex flex-row justify-between">
                    <button class="btn btn-info btn-wide flex flex-row items-center"
                        onclick="window.location.href='/editor/step{{$step}}'"
                        wire:loading.attr="disabled">
                        <template x-if="!loading">
                            <svg xmlns="http://www.w3.org/2000/svg" 
                                class="w-6 h-6 mr-2"
                                width="24" height="24" 
                                fill="currentColor" 
                                viewBox="0 0 256 256">
                                <path d="M245,110.64A16,16,0,0,0,232,104H216V88a16,16,0,0,0-16-16H130.67L102.94,51.2a16.14,16.14,0,0,0-9.6-3.2H40A16,16,0,0,0,24,64V208h0a8,8,0,0,0,8,8H211.1a8,8,0,0,0,7.59-5.47l28.49-85.47A16.05,16.05,0,0,0,245,110.64ZM93.34,64,123.2,86.4A8,8,0,0,0,128,88h72v16H69.77a16,16,0,0,0-15.18,10.94L40,158.7V64Zm112,136H43.1l26.67-80H232Z"></path>
                            </svg>
                        </template>
                        <template x-if="loading">
                            <span class="loading loading-spinner loading-sm"></span>
                        </template>
                        Continuă
                    </button>
                    <button class="btn btn-primary btn-wide flex flex-row items-center"
                        onclick="window.location.href='/editor?abandon=true'"
                        wire:loading.attr="disabled">
                        <svg xmlns="http://www.w3.org/2000/svg" 
                            class="w-6 h-6 mr-2"
                            width="24" height="24" 
                            fill="currentColor" 
                            viewBox="0 0 256 256">
                            <path d="M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34ZM160,51.31,188.69,80H160ZM200,216H56V40h88V88a8,8,0,0,0,8,8h48V216Zm-40-64a8,8,0,0,1-8,8H136v16a8,8,0,0,1-16,0V160H104a8,8,0,0,1,0-16h16V128a8,8,0,0,1,16,0v16h16A8,8,0,0,1,160,152Z"></path>
                        </svg>
                        Document nou
                    </button>
                </form>
            </div>
        </div>
    </div>
</dialog>
@endif

</div>
