<div 
    x-data="{
        theme: localStorage.getItem('theme') || 'light',
        toggle() {
            this.theme = this.theme === 'dark' ? 'light' : 'dark';
            localStorage.setItem('theme', this.theme);
            
            if (this.theme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }
    }"
    x-init="
        if (document.documentElement.classList.contains('dark')) {
            theme = 'dark';
        }
        
        $watch('theme', value => {
            if (value === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        })
    "
    x-on:click="toggle()"
    class="flex items-center px-1 py-2 text-xs rounded-md cursor-pointer select-none hover:bg-zinc-100 dark:hover:bg-zinc-800"
>
    <input type="hidden" name="toggleDarkMode" :value="theme">
    
    <button
        x-ref="toggle"
        type="button"
        role="switch"
        :aria-checked="theme === 'dark'"
        :class="theme === 'dark' ? 'bg-zinc-700' : 'bg-slate-300'"
        class="relative inline-flex flex-shrink-0 py-1 ml-1 transition rounded-full w-7 focus:ring-0"
    >
        <span
            :class="theme === 'dark' ? 'translate-x-[13px]' : 'translate-x-1'"
            class="w-3 h-3 transition bg-white rounded-full shadow-md focus:outline-none"
            aria-hidden="true"
        ></span>
    </button>

    <label
        :class="{ 'text-zinc-600': theme === 'light', 'text-zinc-300': theme === 'dark' }"
        class="flex-shrink-0 ml-1.5 font-medium cursor-pointer"
    >
        <span x-show="theme === 'light'">Dark Mode</span>
        <span x-show="theme === 'dark'">Light Mode</span>
    </label>
</div>
