<script>
    (function() {
        // Get theme from localStorage or system preference
        function getThemePreference() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                return savedTheme;
            }
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }

        // Apply theme to document
        function applyTheme(theme) {
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
            localStorage.setItem('theme', theme);
        }

        // Initialize theme immediately to prevent flash
        const theme = getThemePreference();
        applyTheme(theme);

        // Handle navigation events
        document.addEventListener('livewire:navigated', () => {
            const currentTheme = localStorage.getItem('theme');
            applyTheme(currentTheme);
        });

        // Watch for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
            if (!localStorage.getItem('theme')) {
                const newTheme = e.matches ? 'dark' : 'light';
                applyTheme(newTheme);
            }
        });
    })();
</script>
