@import '/vendor/filament/filament/resources/css/theme.css';
@import '/vendor/awcodes/filament-tiptap-editor/resources/css/plugin.css';
/* 
.fi-topbar nav{
    background:none !important;
    box-shadow:none !important;
}

.fi-sidebar-nav{
    background:#ffffff;
    border-right: 1px solid #f1f1f5;
}

.dark .fi-sidebar-nav{
    background:#18181b;
    border-right:1px solid #23232b;
}

.dark .fi-sidebar-header{
    border-right: 1px solid #23232b;
}

.fi-sidebar-header{
    border-right: 1px solid #f1f1f5;
}

.white-listed{
    @apply text-blue-600;
}

.text-blue-600{
    color:#3662e3;
} */

.text-blue-600{
    color:#3662e3;
}

.fi-topbar > nav, .fi-sidebar-header{
    box-shadow: none !important;
    @apply border-b border-gray-200/60 dark:border-gray-800;
}

/* .fi-url-admin-forms .fi-main{
    @apply px-0 max-w-full h-full bg-zinc-100;
}

.forms-page > section{
    @apply py-0 my-0;
} */

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* For IE, Edge and Firefox */
.scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

.fi-sidebar-header .fi-icon-btn svg{
    display:none;
}


.fi-sidebar-header .fi-icon-btn{
    width:40px; 
    height:40px;
    @apply rounded-lg -translate-x-2 translate-y-px;
}

.fi-sidebar-header .fi-icon-btn:hover{
    @apply bg-gray-100;
}

.fi-sidebar-header .fi-icon-btn::after {
    content: url('data:image/svg+xml;utf8,<svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="none"><path d="M2.74902 6.75C2.74902 5.09315 4.09217 3.75 5.74902 3.75H18.2507C19.9075 3.75 21.2507 5.09315 21.2507 6.75V17.25C21.2507 18.9069 19.9075 20.25 18.2507 20.25H5.74902C4.09217 20.25 2.74902 18.9069 2.74902 17.25V6.75Z" stroke="%23a9a9af" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M10.25 3.75V20.25" stroke="%23a9a9af" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.75 7.75L7.25 7.75" stroke="%23a9a9af" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.75 11L7.25 11" stroke="%23a9a9af" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.75 14.25L7.25 14.25" stroke="%23a9a9af" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g></svg>');
    width: 24px;
    height: 24px;
    display: inline-block; /* Ensure the element can have a color */
}

.fi-sidebar-header .fi-icon-btn:hover::after {
    content: url('data:image/svg+xml;utf8,<svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g fill="none" stroke="none"><path d="M2.74902 6.75C2.74902 5.09315 4.09217 3.75 5.74902 3.75H18.2507C19.9075 3.75 21.2507 5.09315 21.2507 6.75V17.25C21.2507 18.9069 19.9075 20.25 18.2507 20.25H5.74902C4.09217 20.25 2.74902 18.9069 2.74902 17.25V6.75Z" stroke="%2349494f" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M10.25 3.75V20.25" stroke="%2349494f" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.75 7.75L7.25 7.75" stroke="%2349494f" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.75 11L7.25 11" stroke="%2349494f" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.75 14.25L7.25 14.25" stroke="%2349494f" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g></svg>');
    display: inline-block; /* Ensure the element can have a color */
}

.fi-sidebar-header .fi-icon-btn:hover::after {
    color: #ff0000; /* Hover color */
}


@config 'tailwind.config.js';