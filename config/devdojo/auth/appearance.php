<?php

/*
 * Branding configs for your application
 */

return [
    'logo' => [
        'type' => 'svg',
        'image_src' => '',
        'svg_string' => '<svg xmlns="http://www.w3.org/2000/svg" 
    xml:space="preserve" width="30px" height="30px" version="1.1"

    viewBox="0 0 4.5 4.5" fill="none">
    <defs>
        <style type="text/css">
         <![CDATA[
          .fil0 {fill:url(#id0)}
         ]]>
        </style>
        <linearGradient id="id0" gradientUnits="userSpaceOnUse" x1="0.39" y1="4.5" x2="2.13" y2="1.24">
         <stop offset="0" style="stop-opacity:1; stop-color:#FF8800"/>
         <stop offset="1" style="stop-opacity:1; stop-color:#FFAA00"/>
        </linearGradient>
       </defs>
    
    <g class="fil0">

        
        <path  d="M2.83 0l1.28 0c0.21,0 0.39,0.18 0.39,0.39l0 1.98c-0.19,-0.18 -0.5,-0.24 -0.9,-0.06 0.05,0.78 -0.59,1.04 -1.25,1.05 0.05,-0.4 0.12,-0.83 0.19,-1.39 0.08,-0.57 0.18,-1.13 0.26,-1.64 0.01,-0.12 0.02,-0.23 0.03,-0.33zm-2.44 0l0.84 0c0.04,0.39 -0.08,1.05 -0.21,2.05 -0.07,0.6 -0.17,1.21 -0.31,1.96 0.27,-0.09 0.53,-0.14 0.76,-0.14 0.42,0 1.08,0.19 1.86,0.19 0.54,0 0.94,-0.18 1.17,-0.48l0 0.53c0,0.21 -0.18,0.39 -0.39,0.39l-3.72 0c-0.21,0 -0.39,-0.18 -0.39,-0.39l0 -3.72c0,-0.21 0.18,-0.39 0.39,-0.39z"/>
    </g></svg>',
        'height' => '40',
    ],
    'background' => [
        'color' => '#ffffff',
        'image' => '/storage/auth/background.jpg',
        'image_overlay_color' => '#ffffff',
        'image_overlay_opacity' => '1',
    ],
    'color' => [
        'text' => '#00173d',
        'button' => '#000000',
        'button_text' => '#ffffff',
        'input_text' => '#00134d',
        'input_border' => '#232329',
    ],
    'alignment' => [
        'heading' => 'center',
        'container' => 'center',
    ],
    'favicon' => [
        'light' => '/public/favicon.ico',
        'dark' => '/public/favicon.ico',
        //'light' => '/storage/auth/favicon.png',
        //'dark' => '/storage/auth/favicon-dark.png',
    ],
];
