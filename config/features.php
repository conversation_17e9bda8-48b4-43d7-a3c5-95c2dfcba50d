<?php

return [
    (object)[
        'title' => 'Authentication',
        'description' => 'Fully loaded authentication, email verification, and password reset. Authentication in a snap!',
        'image' => '/themes/tailwind/images/authentication.png'
    ],
    (object)[
        'title' => 'User Profiles',
        'description' => 'Customizable user profiles. Allow your users to enter data and easily customize their user profiles.',
        'image' => '/themes/tailwind/images/profile.png'
    ],
    (object)[
        'title' => 'User Impersonation',
        'description' => 'With user impersonations you can login as another user and resolve an issue or troubleshoot a bug.',
        'image' => '/themes/tailwind/images/impersonation.png'
    ],
    (object)[
        'title' => 'Subscriptions',
        'description' => 'Allow users to pay for your service and signup for a subscription using Paddle Payments.',
        'image' => '/themes/tailwind/images/subscriptions.png'
    ],
    (object)[
        'title' => 'Subscription Plans',
        'description' => 'Create new plans with different features and intrigue your users to subscribe to any plan.',
        'image' => '/themes/tailwind/images/plans.png'
    ],
    (object)[
        'title' => 'User Roles',
        'description' => 'Grant user permissions based on roles, you can then assign a role to a specific plan.',
        'image' => '/themes/tailwind/images/roles.png'
    ],
    (object)[
        'title' => 'Notifications',
        'description' => 'Ready-to-use Notification System which integrates with the default Laravel notification feature.',
        'image' => '/themes/tailwind/images/notifications.png'
    ],
    (object)[
        'title' => 'Announcements',
        'description' => 'Create user announcements to notify users about new features or updates in your application.',
        'image' => '/themes/tailwind/images/announcements.png'
    ],
    (object)[
        'title' => 'Blog',
        'description' => 'Equipped with a fully-functional blog. Write posts related to your product to gain free SEO traffic.',
        'image' => '/themes/tailwind/images/blog.png'
    ],
    (object)[
        'title' => 'Fully Functional API',
        'description' => 'Ready-to-consume API for your application. Create API tokens with role specific permissions.',
        'image' => '/themes/tailwind/images/api.png'
    ],
    (object)[
        'title' => 'Voyager Admin',
        'description' => 'Wave has been crafted using Laravel & Voyager, which makes administering your app a breeze!',
        'image' => '/themes/tailwind/images/admin.png'
    ],
    (object)[
        'title' => 'Themes',
        'description' => 'Fully configurable themes. Choose from a few starter themes to begin configuring to make it your own.',
        'image' => '/themes/tailwind/images/themes.png'
    ]
];
