<?php

namespace App\Livewire;


use Livewire\Component;
use App\Models\DocumentDraft;
use App\Models\InfoProfile;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Broadcast;
//use App\Events\TestEvent;
use App\Events\PrivateTestEvent;
use Illuminate\Support\Facades\Auth;
use App\Models\DocumentCategory;
use App\Models\DocumentSubcategory;
use App\Models\Template;


class EditorWizard extends Component
{
    public $categories;
    public $subcategories = [];
    public $templates = [];
    public $selectedCategory;
    public $selectedCategoryName;
    public $selectedSubcategory;
    public $selectedTemplate;
    public $step;


    
    public $documentDraft;
    public $part_a;
    public $part_b;
    public $part_c;
    public $slots;
    public $infoProfiles;
    public $dataComplete;
    public $documentCategory;
    public $infoProfileIdA;
    public $infoProfileIdB;
    public $infoProfileIdC;
    public $isModalOpen = false;
    public $modalSelected = '';

    protected $listeners = ['infoProfileSaved' => 'infoProfileSaved', 'goNext' => 'nextStep', 'goBack' => 'backStep'];

    

    //protected $listeners = ['stepChanged' => 'handleNextStep'];

    public function mount()
    {

    $this->step = session('step', 1);
    
    $this->initialize();
    }

    public function initialize()
    {

        if ($this->step == 1) {

            $this->categories = DocumentCategory::with('documentSubcategories')->get();

            } elseif ($this->step == 2) {

                $this->documentDraft = DocumentDraft::where('user_id', auth()->id())->first();
                $this->documentCategory = $this->documentDraft->template->category->id;

                    if (in_array($this->documentCategory, [1, 3])) {
                        $this->slots = 1;
                    } elseif (in_array($this->documentCategory, [2, 4])) {
                        $this->slots = 2;
                    } else {
                        $this->slots = 0; // Default case if needed
                    }

            
                        $this->infoProfileIdA = $this->documentDraft->part_a;
                        $this->infoProfileIdB = $this->documentDraft->part_b;
                        $this->infoProfileIdC = $this->documentDraft->part_c;
                        Log::info($this->infoProfileIdA);  
                                    
                
            }
    }



    public function refreshProfiles($infoProfileId)
    {
        // Optionally, you can fetch the new profile details
        $newProfile = InfoProfile::find($infoProfileId);

        if ($newProfile) {
            if ($newProfile->is_client) {
                $this->clientProfiles->push($newProfile);
            } else {
                $this->nonClientProfiles->push($newProfile);
            }
        }

        // Optional: Flash message
        session()->flash('success', 'New InfoProfile added successfully.');
    }



    public function openModalA()
    {
        $this->modalSelected = 'A';
        $this->isModalOpen = true;
        //$this->emit('openModalA');
    }

    public function openModalB()
    {
        $this->modalSelected = 'B';
        $this->isModalOpen = true;
        //$this->emit('openModalB');
    }

    public function openModalC()
    {
        $this->modalSelected = 'C';
        $this->isModalOpen = true;
        //$this->emit('openModalC');
    }



    public function infoProfileSaved($payload)
    {
     $this->isModalOpen = false;
     $this->modalSelected = '';
     $this->trigger('Info Profile Saved');
    }
   

    public function updatedSelectedCategory($categoryId)
    {
        // Locate the chosen category in our already-loaded $categories collection
        $category = $this->categories->firstWhere('id', $categoryId);
    
        // If found, assign its subcategories; otherwise empty array/collection
        $this->subcategories = $category
            ? $category->documentSubcategories
            : [];
        
        $this->selectedCategoryName = $category->name;
        // Reset templates
        $this->templates = [];
    }
    

    public function updatedSelectedSubcategory($subcategoryId)
    {
        // Fetch the templates tied to that subcategory ID
        $this->templates = Template::where('subcategory_id', $subcategoryId)->get();
    }

  
    public function updatedSelectedTemplate($templateId)
    {
       
    $this->dispatch('enableNextStep');
        // You can add any logic here that should run when the selectedTemplate is updated
        // For example, you might want to log the change or perform some validation
        // Log::info("Template selected: " . $templateId);
    }
    public function showNextStepButton(){
        
    }

    public function backStep($step){
       
      
        $this->step = session('step');
        $this->initialize();
      //  $this->step = $step;
     //   session(['step' => $this->step]);
       
    }


    public function nextStep(){

        $documentDraft = new DocumentDraft();
        $documentDraft->user_id = Auth::id();
        $documentDraft->template_id = $this->selectedTemplate;
        $documentDraft->save();

        $this->initialize();

        $this->step = session('step');
      //  session(['step' => $this->step]);

        //$this->render();
      //  $this->dispatch('stepChanged', ['step' => $step]);
    }

  

 public function render()
             {
      
   // }
        return view('livewire.editor-wizard');
             }
}
