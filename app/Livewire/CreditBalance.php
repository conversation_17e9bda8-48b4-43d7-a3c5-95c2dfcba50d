<?php

namespace App\Livewire;

use Livewire\Component;
use App\Services\CreditService;

class CreditBalance extends Component
{
    public $balance;
    public $transactions;

    public function mount(CreditService $creditService)
    {
        $user = auth()->user();
        $creditService->initializeBalance($user);
        
        $this->balance = $creditService->getBalance($user);
        $this->transactions = $creditService->getTransactionHistory($user, 5);
    }

    public function render()
    {
        return view('livewire.credit-balance');
    }
}
