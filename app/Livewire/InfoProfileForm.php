<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\InfoProfile;
use App\Models\DocumentDraft;
use Livewire\Attributes\On; 
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class InfoProfileForm extends Component
{
    public $isModalOpen;
    public $documentDraft;
    public $infoProfile;
    public $initialTab, $button1, $button2;
        public $entityType = 'individual';
        public $first_name, $last_name, $pronoun, $email, $phone, $date_of_birth, $place_of_birth, $county_of_birth;
        public $address_street, $address_number, $address_block, $address_entrance, $address_floor, $address_apt, $address_city, $address_county, $address_country;
        public $id_document_type, $id_document_series, $id_document_number, $id_document_issuer, $id_document_issue_date, $personal_id_number;
        public $bank_name, $bank_city, $iban ;
        public $postal_code, $country_of_birth;
        public $company_name, $company_role, $tax_identification_number, $registration_number, $registration_county;
        public $part;



        public function mount($part)
        {
            $this->part = $part;
            $this->documentDraft = DocumentDraft::where('user_id', Auth::id())->first();

            if ($this->documentDraft) {
                // Load the InfoProfile if it exists for this part
                $profileId = $this->documentDraft->{$this->part};
                
                if ($profileId) {
                    $this->infoProfile = InfoProfile::find($profileId);
                    
                    if ($this->infoProfile) {
                        // Populate the form fields
                    $this->initialTab = $this->infoProfile->entity_type === 'company' ? 2 : 1;
                    $this->first_name = $this->infoProfile->first_name;
                    $this->last_name = $this->infoProfile->last_name;
                    $this->pronoun = $this->infoProfile->pronoun;
                    $this->email = $this->infoProfile->email;
                    $this->phone = $this->infoProfile->phone;
                    $this->date_of_birth = Carbon::parse($this->infoProfile->id_document_issue_date)->toDateString();
                    $this->place_of_birth = $this->infoProfile->place_of_birth;
                    $this->county_of_birth = $this->infoProfile->county_of_birth;
                    $this->address_street = $this->infoProfile->address_street;
                    $this->address_number = $this->infoProfile->address_number;
                    $this->address_block = $this->infoProfile->address_block;
                    $this->address_entrance = $this->infoProfile->address_entrance;
                    $this->address_floor = $this->infoProfile->address_floor;
                    $this->address_apt = $this->infoProfile->address_apt;
                    $this->address_city = $this->infoProfile->address_city;
                    $this->address_county = $this->infoProfile->address_county;
                    $this->address_country = $this->infoProfile->address_country;
                    $this->id_document_type = $this->infoProfile->id_document_type;
                    $this->id_document_series = $this->infoProfile->id_document_series;
                    $this->id_document_number = $this->infoProfile->id_document_number;
                    $this->id_document_issuer = $this->infoProfile->id_document_issuer;
                    $this->id_document_issue_date = Carbon::parse($this->infoProfile->id_document_issue_date)->toDateString();
                    $this->personal_id_number = $this->infoProfile->personal_id_number;
                    $this->bank_name = $this->infoProfile->bank_name;
                    $this->bank_city = $this->infoProfile->bank_city;
                    $this->iban = $this->infoProfile->iban;
                    $this->company_name = $this->infoProfile->company_name;
                    $this->tax_identification_number = $this->infoProfile->tax_identification_number;
                    $this->registration_number = $this->infoProfile->registration_number;
                    $this->registration_county = $this->infoProfile->registration_county;
                    $this->company_role = $this->infoProfile->company_role;
                    $this->postal_code = $this->infoProfile->postal_code;
                    $this->country_of_birth = $this->infoProfile->country_of_birth;

                    }
                }
            }
        }

        
    







       


        public function cancel()
        {
            $this->reset();
            // Dispatch an event to the parent to close the modal
            $this->dispatch('infoProfileCanceled');
            
            // Reset the form
            
        }

        public function save($type)
        {
            $data = [
                'entity_type' => $type,
                'first_name' => $this->first_name,
                'last_name' => $this->last_name,
                'email' => $this->email,
                'phone' => $this->phone,
                'date_of_birth' => $this->date_of_birth,
                'place_of_birth' => $this->place_of_birth,
                'county_of_birth' => $this->county_of_birth,
                'address_street' => $this->address_street,
                'address_number' => $this->address_number,
                'address_block' => $this->address_block,
                'address_entrance' => $this->address_entrance,
                'address_floor' => $this->address_floor,
                'address_apt' => $this->address_apt,
                'address_city' => $this->address_city,
                'address_county' => $this->address_county,
                'address_country' => $this->address_country,
                'tax_identification_number' => $this->tax_identification_number,
                'registration_number' => $this->registration_number,
                'registration_county' => $this->registration_county,
                'postal_code' => $this->postal_code,
                'country_of_birth' => $this->country_of_birth,
                'pronoun' => $this->pronoun,
                'id_document_type' => $this->id_document_type,
                'id_document_series' => $this->id_document_series,
                'id_document_number' => $this->id_document_number,
                'id_document_issuer' => $this->id_document_issuer,
                'id_document_issue_date' => $this->id_document_issue_date,
                'personal_id_number' => $this->personal_id_number,
                'bank_name' => $this->bank_name,
                'bank_city' => $this->bank_city,
                'iban' => $this->iban,
                'company_name' => $this->company_name,
                'company_role' => $this->company_role,
            ];

        //    / dd($data);
                if ($this->infoProfile) {
                    $this->infoProfile->update($data);
                } else {
                    $this->infoProfile = InfoProfile::create($data + ['user_id' => Auth::id()]);
                }
               // dd($this->part);
                // Store the ID in the DocumentDraft
                $this->documentDraft->{$this->part} = $this->infoProfile->id;
                $this->documentDraft->save();
                
           //     $this->isModalOpen = false;
                Log::info('Dispatching infoProfileSaved event', [
                    'infoProfileId' => $this->infoProfile->id,
                    'part' => $this->part,
                ]);
                
                // Dispatch event with the infoProfileId and the part
                $this->dispatch('infoProfileSaved', [
                    'infoProfileId' => $this->infoProfile->id,
                    'part' => $this->part,
                ]);
                
                // After successful save, dispatch the close event
              //  $this->dispatch('closeModal')->to('step02');
        }
        
       
        public function rules()
        {
            return [];
        }
    
  
    
    public function render()
    {
        return view('livewire.info-profile-form');
    }
}
