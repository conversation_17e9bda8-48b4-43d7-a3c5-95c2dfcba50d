<?php

namespace App\Livewire;

use Livewire\Component;

class HeaderStepButtons extends Component
{
    public $step;
    public $showNextStep;
    public $showFormerStep;
    public $nextStep;
    public $previousStep;
    public $nextEnabled;
    public $formerEnabled;
    public $showReset;


    protected $listeners = ['enableNextStep' => 'enableNextStep', 'enableFormerStep' => 'enableFormerStep'];

    public function mount()
    {
        $this->formerEnabled = true;
        $this->step = session('step', 0);

        if (request()->route()->getName() !== 'editor') {
            $this->showFormerStep = false;
            $this->showNextStep = false;

        if ($this->step < 1) {
            $this->showReset = true;
        }

        } else {
          //  $this->$showFormerStep = true;
         //   $this->$showNextStep = true;
       

                    if ($this->step >= 1 && $this->step <= 3) {
                        $this->showNextStep = true;
                    }

                    if ($this->step >= 2 && $this->step <= 4) {
                        $this->showFormerStep = true;
                    }
               }

       // $this->nextStep = $this->step + 1;
      //  $this->previousStep = $this->step - 1;
        
    }


    public function enableNextStep()
    {
        $this->nextEnabled = true;

    }

    public function goBackward()
    {
        $this->step--;
        session(['step' => $this->step]);

        //return redirect()->route('editor', ['step' => $this->step]);
        
        $this->dispatch('goBack', $this->step);
        $this->formerEnabled = false;
    }

    public function goForward()
    {
        $this->step++;
        session(['step' => $this->step]);

        //return redirect()->route('editor', ['step' => $this->step]);
        
        $this->dispatch('goNext', $this->step);
    }

    public function render()
    {
        return view('livewire.header-step-buttons');
    }
}
