<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\InfoProfile;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On;


class TextPreview extends Component
{

    public $text;
    public $part;
    public $infoProfileId;

   // protected $listeners = [
   //     'infoProfileSaved' => 'handleInfoProfileSaved',
   // ];

   // protected $listeners = ['infoProfileSaved'];

   protected $listeners = ['infoProfileSaved' => 'infoProfileSaved'];

    
   
    public function infoProfileSaved($payload)
    {
        Log::info($payload);
        if ($payload['part'] === $this->part) {
            $infoProfile = InfoProfile::find($payload['infoProfileId']);
            $this->text = $this->prepareText($infoProfile);
        }
    }

//     #[On('infoProfileSaved')]
//     public function handleInfoProfileSaved($payload)
// {

//     Log::info($payload);
//     if ($payload['part'] === $this->part) {
//         $infoProfile = InfoProfile::find($payload['infoProfileId']);
//         $this->text = $this->prepareText($infoProfile);
//     }
    
// }
    public function mount($infoProfileId, $part)
    {
        $this->part = $part;
        $this->infoProfileId = $infoProfileId;
        if (empty($infoProfileId)) {
            $this->text = "Nu există date introduse.";
           // return;
        } else {
            $infoProfile = InfoProfile::find($infoProfileId);
            $this->text = $this->prepareText($infoProfile);
        }
       
    }

    public function prepareText($infoProfile)
    {
        $identificat = $infoProfile->pronoun == 'Dl.' ? 'identificat' : 'identificată';
        $eliberat = $infoProfile->pronoun == 'CI' ? 'eliberată' : 'eliberat';

        if ($infoProfile->entity_type == 'company') {

            $identificat = $infoProfile->pronoun == 'Dl.' ? 'identificat' : 'identificată';
            $eliberat = $infoProfile->pronoun == 'CI' ? 'eliberată' : 'eliberat';

            $text = "{$infoProfile->company_name} cu sediul în {$infoProfile->address},
            înregistrată la Registrul Comerțului {$infoProfile->registration_county},
            sub nr. J{$infoProfile->registration_number},
            având cod unic de înregistrare {$infoProfile->tax_identification_number},
            cont IBAN RO{$infoProfile->iban} deschis la {$infoProfile->bank_name} {$infoProfile->bank_city},
            email {$infoProfile->email}, telefon {$infoProfile->phone}
            reprezentată legal de {$infoProfile->pronoun} {$infoProfile->first_name} {$infoProfile->last_name},
            {$identificat} cu {$infoProfile->id_document_type}, seria {$infoProfile->id_document_series}, nr. {$infoProfile->id_document_number},
            {$eliberat} de {$infoProfile->id_document_issuer}, la data de {$infoProfile->id_document_issue_date}, în calitatea de {$infoProfile->company_role} [...]";
            return $text;
        } else {
            $nascut = $infoProfile->pronoun == 'Dl.' ? 'născut' : 'născută';
            $text = "{$infoProfile->pronoun} {$infoProfile->last_name} {$infoProfile->first_name}, {$nascut} la data de {$infoProfile->date_of_birth}, în {$infoProfile->place_of_birth}, județul {$infoProfile->county_of_birth},
            domiciliat în {$infoProfile->address}, CNP {$infoProfile->personal_id_number}, identificat cu {$infoProfile->id_document_type}, seria {$infoProfile->id_document_series}, nr. {$infoProfile->id_document_number},
            {$eliberat} de {$infoProfile->id_document_issuer}, la data de {$infoProfile->id_document_issue_date}, cont IBAN {$infoProfile->iban} deschis la {$infoProfile->bank_name} {$infoProfile->bank_city},
            email {$infoProfile->email}, telefon {$infoProfile->phone}, [...]";
            return $text;
        };

        //  $this->text = $text;
    }

    public function render()
    {
        return view('livewire.text-preview');
    }
}
