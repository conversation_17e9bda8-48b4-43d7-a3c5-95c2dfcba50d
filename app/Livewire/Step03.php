<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\InfoProfile;
use App\Models\Logo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\DocumentDraft;
use App\Models\Template;
use App\Models\DocumentBrick;
use Illuminate\Support\Facades\View;
use Spatie\Browsershot\Browsershot;
use App\Models\Document;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use Gotenberg\Gotenberg;
use Gotenberg\Stream;
use Gotenberg\Index;
use Gotenberg\Exceptions\GotenbergApiErrored;
use App\Models\Pdfable;
use GuzzleHttp\Client;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Imagick;

class Step03 extends Component
{    
    public $templateId;
    public $template;
    public $subcategory;
    public $content;
    public $pages = [];
    public $currentPage = 1;
    public $zoomLevel = 1;

    // Variables for placeholders
    public $variables = [];

    // Numbering counters
    public $sectionNumber = 0;
    public $subsectionNumber = 0;
    public $paragraphNumber = 0;
    public $listItemNumber = 0;
    public $draft;
    public $numberingStack;
    protected $inExcludedContent = false;
    // Flags for numbering within conditional blocks
    protected $inConditionalBlock = false;
    public $rawContent;


    protected $listeners = ['finalizeDocument', 'saveDraft'];

    public function mount()
    {
        $user = Auth::user();
        if (DocumentDraft::where('user_id', $user->id)->first()) {
            $this->draft = DocumentDraft::where('user_id', $user->id)->first();
            $this->templateId = $this->draft->template_id;
            $this->template = Template::with('documentBricks')->findOrFail($this->templateId);
            $this->subcategory = $this->template->subcategory->id;
        //    Log::info($this->subcategory);
        }
        $this->content =  $this->prepareData($this->template);
    }

    public function prepareData($template)
    {




        $content = '';

        foreach ($template->documentBricks as $brick) {
            // Decode HTML entities if necessary
            $brickContent = html_entity_decode($brick->content);

            $content .= $brickContent . "\n\n";
        }

        $this->rawContent = $content;
        // Process the content (parse placeholders, etc.)
        $content = $this->processContent($content);

        //  return $processedContent;


        // Post-process the HTML to ensure proper formatting
        $content = $this->postProcessHTML($content, false);

        // Load variables from draft if available
        $this->loadDraft();

        return $content;
    }


    protected function postProcessHTML($content, $finalizing)
    {

        $content = preg_replace('/(?!^)(<h[1-6]>)/m', "\n$1", $content);
        $content = preg_replace('/(?!^)(<\/h[1-6]>)/m', "$1\n", $content);
        $content = preg_replace('/(?!^)(<p>)/m', "\n$1", $content);
        $content = preg_replace('/(?!^)(<\/p>)/m', "$1\n", $content);

        if ($finalizing == false) {
        } // Add new lines before and after headings and paragraphs

        return $content;
    }

    public function saveDraft()
    {
        try {
            $draft = DocumentDraft::updateOrCreate(
                ['user_id' => Auth::id()],
                ['content' => $this->content, 'variables' => json_encode($this->variables)]
            );

            $draft->save();
            $this->dispatch('enableNext');
            session()->flash('message', 'Draft saved successfully.');

            $this->dispatch('loading-end');
        } catch (\Exception $e) {
            $this->dispatch('loading-end');
            throw $e;
        }
    }

    protected function loadDraft()
    {
        $this->draft = DocumentDraft::where('user_id', Auth::id())->first();
        if ($this->draft) {
            $this->variables = json_decode($this->draft->variables, true) ?? [];
            $this->content = $this->draft->content;
        }
    }

    public function finalizeDocument()
    {

        try {
            // Process conditional blocks based on variables
            $finalContent = $this->parseConditionalBlocks($this->rawContent, true);

            // Reset numbering counters
            $this->sectionNumber = 0;
            $this->subsectionNumber = 0;
            $this->paragraphNumber = 0;

            // Parse numbering placeholders
            $finalContent = $this->parseNumberingPlaceholders($finalContent, true);

            // Replace parties with actual values

            $finalContent = $this->replaceParties($finalContent);

            // Replace placeholders with variable values
            $finalContent = $this->replacePlaceholdersWithValues($finalContent);


            $finalContent = $this->postProcessHTML($finalContent, true);

            // Save the finalized content to DocumentDraft
            $this->draft->final_content = $finalContent;
            $this->draft->save();

            // Generate PDF
            $repInfo = $this->getRepresentativeInfo();
            // 1. Directory Setup
            $userId = Auth::id();
           
            //  Log::info($this->template->name);
            //  Log::info($finalContent);

            // 1. Prepare storage directory
            $userId = Auth::id();
            $userDir = "documents/{$userId}";
            Storage::disk('public')->makeDirectory($userDir);

            // 2. Generate unique filename
            $categorySlug = Str::slug($this->template->category->name);
            $date = Carbon::now()->format('Y-m-d');
            $hash = Str::random(8);
            $baseFilename = "{$categorySlug}-{$date}-{$hash}";
            
            $pdfPath = "{$userDir}/{$baseFilename}.pdf";
            $jpgPath = "{$userDir}/{$baseFilename}.jpg";

            // 3. Create PDFable model
            $pdfable = Pdfable::create([
                'template_title' => $this->template->name,
                'content' => $finalContent,
                'party_a_rep_name' => $repInfo['partyA_rep_name'],
                'party_a_rep_role' => $repInfo['partyA_rep_role'],
                'party_b_rep_name' => $repInfo['partyB_rep_name'],
                'party_b_rep_role' => $repInfo['partyB_rep_role'],
                'document_category' => $this->template->category->id,
                'user_id' => Auth::id()
            ]);

            $url = route('pdf.generate', $pdfable);
            Log::info('Generated URL for Gotenberg: ' . $url);

            // 4. Generate PDF with Gotenberg
            $httpClient = new Client();
            $apiUrl = 'http://gotenberg:3000';

            $request = Gotenberg::chromium($apiUrl)
                ->pdf()
                ->waitDelay('1.5s')
                ->skipNetworkIdleEvent(false)
                ->url($url);

            $response = $httpClient->sendRequest($request);
            
            if ($response->getStatusCode() !== 200) {
                Log::error('Gotenberg response error: ' . $response->getStatusCode() . ' - ' . $response->getBody()->getContents());
                throw new \Exception('Gotenberg API Error: ' . $response->getStatusCode() . ' - ' . $response->getBody()->getContents());
            }

            // 5. Store PDF
            Storage::disk('public')->put($pdfPath, $response->getBody()->getContents());

            // 6. Generate JPG preview using Ghostscript

            $pdfFullPath = Storage::disk('public')->path($pdfPath);
            $jpgFullPath = Storage::disk('public')->path($jpgPath);
             
            try {
                // Define the Ghostscript path directly since we know where it should be installed
                $gsPath = '/usr/bin/gs';
                
                // Check if the file exists and is executable
                if (!file_exists($gsPath) || !is_executable($gsPath)) {
                    Log::error("Ghostscript not found at {$gsPath} or not executable");
                    throw new \Exception("Ghostscript not properly installed");
                }
                
                // Log the paths we're using
                Log::info("PDF path: {$pdfFullPath}");
                Log::info("JPG path: {$jpgFullPath}");
                Log::info("Ghostscript path: {$gsPath}");

                // Calculate DPI for 600px height (A4 = 11.69 inches height)
                $dpi = round(600 / 11.69);

                // Build the command
                $gsCommand = sprintf(
                    '%s -sDEVICE=jpeg -dFirstPage=1 -dLastPage=1 -sOutputFile=%s -r%d -dJPEGQ=60 -dGraphicsAlphaBits=4 -dTextAlphaBits=4 -dSubPixel=true -dNOPAUSE -dBATCH -dSAFER %s',
                    escapeshellcmd($gsPath),
                    escapeshellarg($jpgFullPath),
                    $dpi,
                    escapeshellarg($pdfFullPath)
                );
                
                // Log the command we're about to execute
                Log::info("Executing command: {$gsCommand}");
                
                // Execute the command and capture both stdout and stderr
                $output = [];
                $returnCode = -1;
                exec($gsCommand . " 2>&1", $output, $returnCode);

                if ($returnCode !== 0) {
                    Log::error("Ghostscript failed with return code: " . $returnCode);
                    Log::error("Command output: " . implode("\n", $output));
                    throw new \Exception("Ghostscript failed with return code: " . $returnCode);
                }

                // Verify the JPG was created
                if (!file_exists($jpgFullPath)) {
                    throw new \Exception("JPG file was not created at: {$jpgFullPath}");
                }

                Log::info("Successfully generated JPG preview at: {$jpgFullPath}");

            } catch (\Exception $e) {
                Log::error("JPG preview generation failed: " . $e->getMessage());
                throw $e;
            }

            // 7. Create Document record
            $document = Document::create([
                'user_id' => $userId,
                'template_id' => $this->templateId,
                'subcategory_id' => $this->template->subcategory_id,
                'category_id' => $this->template->category_id,
                'document_number' => 'DOC-' . date('Y') . sprintf('%06d', Document::count() + 1),
                'status' => 'generated',
               // 'design_id' => 1,
                'uuid' => (string) Str::uuid(),
                'document_url' => Storage::disk('public')->url($pdfPath),
                'thumbnail' => Storage::disk('public')->url($jpgPath),
                'was_paid_for' => ($this->template->tier_id <= 1) // Free documents are marked as paid
            ]);
            Log::info('PDF:'. Storage::disk('public')->url($pdfPath));
            Log::info('JPG:'. Storage::disk('public')->url($jpgPath));
            Log::info('Document created: ' . $document);
            // 8. Store URLs in session
            session([
                'document_pdf_url' => $document->document_url,
                'document_preview_url' => $document->thumbnail,
                'step' => 4
            ]);
            return redirect()->route('editor.step4');

        } catch (\Exception $e) {
            Log::error('PDF Generation Error: ' . $e->getMessage());
            return null;
        }

      //  $this->dispatch('loading-end');
    }

    protected function getRepresentativeInfo()
    {
        $repInfo = [
            'partyA_rep_name' => '',
            'partyA_rep_role' => '',
            'partyB_rep_name' => '',
            'partyB_rep_role' => ''
        ];
    
        if ($this->draft->part_a) {
            $partyA = InfoProfile::find($this->draft->part_a);
            if ($partyA) {
                $repInfo['partyA_rep_name'] = $partyA->entity_type === 'company' 
                    ? "{$partyA->pronoun} {$partyA->first_name} {$partyA->last_name}"
                    : '';
                $repInfo['partyA_rep_role'] = $partyA->entity_type === 'company' 
                    ? $partyA->company_role 
                    : '';
            }
        }
    
        if ($this->draft->part_b) {
            $partyB = InfoProfile::find($this->draft->part_b);
            if ($partyB) {
                $repInfo['partyB_rep_name'] = $partyB->entity_type === 'company' 
                    ? "{$partyB->pronoun} {$partyB->first_name} {$partyB->last_name}"
                    : '';
                $repInfo['partyB_rep_role'] = $partyB->entity_type === 'company' 
                    ? $partyB->company_role 
                    : '';
            }
        }
    
        return $repInfo;
    }

    protected function replacePlaceholdersWithValues($content)
    {

        // Replace placeholders with actual values
        $content = preg_replace_callback('/\{\{\s*(.*?)\s*\}\}/', function ($matches) {
            $variableName = $matches[1];
            $value = $this->variables[$variableName] ?? '';
            return htmlspecialchars($value);
        }, $content);

        return $content;
    }


    protected function processContent($content)
    {
        $content = $this->parseNumberingPlaceholders($content, false);
        //  Log::info('after numbering' . $content);
        // Handle conditional blocks first
        $content = $this->parseConditionalBlocks($content, false);
        //  Log::info('after conditional' . $content);


        // Handle and placeholders
        $content = $this->parsePlaceholders($content);
        //  Log::info('after placeholders' . $content);
        return $content;
    }


    protected function parseConditionalBlocks($content, $finalizing)
    {
        $pattern = '/\{\{\s*\?\s*:(.*?)\s*\}\}(.*?)\{\{\/\?\}\}/s';

        return preg_replace_callback($pattern, function ($matches) use ($finalizing) {
            $variableName = trim($matches[1]);
            $blockContent = $matches[2];

            if ($finalizing == true) {
                // During finalization, include or exclude content based on checkbox value
                if ($this->variables[$variableName] ?? false) {
                    // Process nested conditional blocks and placeholders within this block
                    $processedContent = $this->parseConditionalBlocks($blockContent, true);
                 //   Log::info($processedContent);
                    //    $processedContent = $this->parseNumberingPlaceholders($processedContent);
                    $processedContent = $this->replacePlaceholdersWithValues($processedContent);
                    static $iterationCounter = 0;
                    $iterationCounter++;
                 //   Log::info("Iteration $iterationCounter: " . $processedContent);
                    return $processedContent;
                } else {
                    // Exclude the content
                    return '';
                }
            } else {
                // During editing, render the checkbox and content
                $checkbox = $this->renderConditionalCheckbox($variableName, $blockContent);
                return $checkbox;
            }
        }, $content);
    }


    protected function replaceParties($content)
    {
        $content = preg_replace_callback('/\{\{\s*(partyA|partyB|partyer|partyee)\s*\}\}/', function ($matches) {
            $placeholder = trim($matches[1]);

            switch ($placeholder) {
                case 'partyA':
                    return $this->renderPartyA();
                case 'partyB':
                    return $this->renderPartyB();
                case 'partyer':
                    return $this->renderPartyer();
                case 'partyee':
                    return $this->renderPartyee();
                default:
                    return $matches[0]; // Should not reach here
            }
        }, $content);

        return $content;
    }


    protected function parsePlaceholders($content)
    {

        $content = $this->replaceParties($content);

        // Handle variable placeholders
        $content = preg_replace_callback('/\{\{\s*(.*?)\s*\}\}/', function ($matches) {
            $placeholder = trim($matches[1]);

            // Check for input types
            if (strpos($placeholder, 'date:') === 0) {
                $variableName = substr($placeholder, 5);
                return $this->renderDateInput($variableName);
            } elseif (strpos($placeholder, 'num:') === 0) {
                $variableName = substr($placeholder, 4);
                return $this->renderNumericInput($variableName);
            } elseif (strpos($placeholder, 'txt:') === 0) {
                $variableName = substr($placeholder, 4);
                return $this->renderTextInput($variableName);
            } elseif (strpos($placeholder, 'select:') === 0) {
                $parts = explode('|', substr($placeholder, 7));
                $variableName = array_shift($parts);
                $options = $parts;
                return $this->renderSelectInput($variableName, $options);
            } else {
                // Regular variable (fallback to text input)

                return $this->renderTextInput($placeholder);
            }
        }, $content);


        return $content;
    }


    protected function renderPartyA()
    {
        return $this->preparePartyText($this->draft->part_a);
        //return htmlspecialchars($this->draft->part_a ?? ''); // Safely escape output
    }


    public function preparePartyText($infoProfile)
    {
        $infoProfile = InfoProfile::find($infoProfile);

        // Set up representative name and role variables
        if ($this->draft->part_a == $infoProfile->id) {
            $this->variables['partyA_rep_name'] = $infoProfile->entity_type == 'company' 
                ? "{$infoProfile->pronoun} {$infoProfile->first_name} {$infoProfile->last_name}"
                : '';
            $this->variables['partyA_rep_role'] = $infoProfile->entity_type == 'company' 
                ? $infoProfile->company_role 
                : '';
        } else if ($this->draft->part_b == $infoProfile->id) {
            $this->variables['partyB_rep_name'] = $infoProfile->entity_type == 'company' 
                ? "{$infoProfile->pronoun} {$infoProfile->first_name} {$infoProfile->last_name}"
                : '';
            $this->variables['partyB_rep_role'] = $infoProfile->entity_type == 'company' 
                ? $infoProfile->company_role 
                : '';
        }

        $identificat = $infoProfile->pronoun == 'Dl.' ? 'identificat' : 'identificată';
        $eliberat = $infoProfile->pronoun == 'CI' ? 'eliberată' : 'eliberat';
        
        if ($infoProfile->entity_type == 'company') {
            $numit = 'numită';
        } else {
            $numit = $infoProfile->pronoun == 'Dl.' ? 'numit' : 'numită';
        };

        if ($infoProfile->entity_type == 'company') {
            $identificat = $infoProfile->pronoun == 'Dl.' ? 'identificat' : 'identificată';
            $eliberat = $infoProfile->pronoun == 'CI' ? 'eliberată' : 'eliberat';

            $text = "{$infoProfile->company_name} cu sediul în {$infoProfile->address},
            înregistrată la Registrul Comerțului {$infoProfile->registration_county},
            sub nr. J{$infoProfile->registration_number},
            având cod unic de înregistrare {$infoProfile->tax_identification_number},
            cont IBAN RO{$infoProfile->iban} deschis la {$infoProfile->bank_name} {$infoProfile->bank_city},
            email {$infoProfile->email}, telefon {$infoProfile->phone}
            reprezentată legal de {$infoProfile->pronoun} {$infoProfile->first_name} {$infoProfile->last_name},
            {$identificat} cu {$infoProfile->id_document_type}, seria {$infoProfile->id_document_series}, nr. {$infoProfile->id_document_number},
            {$eliberat} de {$infoProfile->id_document_issuer}, la data de {$infoProfile->id_document_issue_date}, în calitatea de {$infoProfile->company_role} ";
            
          //  Log::info($text);
            return $text;
        } else {
            $nascut = $infoProfile->pronoun == 'Dl.' ? 'născut' : 'născută';
            $text = "{$infoProfile->pronoun} {$infoProfile->last_name} {$infoProfile->first_name}, {$nascut} la data de {$infoProfile->date_of_birth}, în {$infoProfile->place_of_birth}, județul {$infoProfile->county_of_birth},
            domiciliat în {$infoProfile->address}, CNP {$infoProfile->personal_id_number}, identificat cu {$infoProfile->id_document_type}, seria {$infoProfile->id_document_series}, nr. {$infoProfile->id_document_number},
            {$eliberat} de {$infoProfile->id_document_issuer}, la data de {$infoProfile->id_document_issue_date}, cont IBAN {$infoProfile->iban} deschis la {$infoProfile->bank_name} {$infoProfile->bank_city},
            email {$infoProfile->email}, telefon {$infoProfile->phone}, ";
            
         //  Log::info($text);
            return $text;
        };

        //  $this->text = $text;
    }

    protected function renderPartyB()
    {

        return $this->preparePartyText($this->draft->part_b);
        // return htmlspecialchars($this->draft->part_b ?? '');
    }

    protected function renderPartyer()
    {
        // Example logic based on subcategory
        if ($this->subcategory == '1') {
            return 'Prestator';
        } elseif ($this->subcategory == '2') {
            return 'Vânzător';
        }

        // Default value
        return 'Default Partyer';
    }

    protected function renderPartyee()
    {

        // Example logic based on subcategory
        if ($this->subcategory == '1') {
            return 'Beneficiar';
        } elseif ($this->subcategory == '2') {
            return 'Cumpărător';
        }

        // Default value
        return 'Default Partyee';
    }

    protected function parseNumberingPlaceholders($content, $finalizing)
    {

        if ($finalizing) {
            // Use regex to find placeholders and replace them
             $content = preg_replace_callback('/\{\{(#+)\}\}/', function ($matches) {
                 $hashes = $matches[1];
                 $replacement = '';
                 return $replacement;
             }, $content);
        } else {
            $content = preg_replace_callback('/\{\{(#+)\}\}/', function ($matches) {
                $hashes = $matches[1];
                $replacement = '';
                return $replacement;
            }, $content);
        }

        //  Log::info($content);
        return $content;
    }


        /**
     * Render a date input.
     */
    protected function renderDateInput($variableName)
    {
        $value = $this->variables[$variableName] ?? '';

        // Render date input field
        return '<input type="date" wire:model.blur="variables.' . $variableName . '" class="inline-input" />';
    }

    /**
     * Render a numeric input.
     */
    protected function renderNumericInput($variableName)
    {
        $value = $this->variables[$variableName] ?? '';

        // Render numeric input field
        return '<input type="number" wire:model.blur="variables.' . $variableName . '" class="ml-2 input input-bordered max-w-xs input-sm text-end" />';
    }

    /**
     * Render a text input.
     */
    protected function renderTextInput($variableName)
    {
        $value = $this->variables[$variableName] ?? '';

        // Render text input field
        return '<input type="text" wire:model.blur="variables.' . $variableName . '" class="ml-2 input input-bordered max-w-xs input-sm text-end" />';
    }

    /**
     * Render a select input.
     */
    protected function renderSelectInput($variableName, $options)
    {
        $value = $this->variables[$variableName] ?? '';

        $html = '<select wire:model.live="variables.' . $variableName . '" class="select select-xs select-ghost max-w-xs mx-2 p-0">';
        $html .= '<option value="">Alege o opțiune</option>';
        foreach ($options as $option) {
            $selected = $value === $option ? 'selected' : '';
            $html .= '<option value="' . htmlspecialchars($option) . '" ' . $selected . '>' . htmlspecialchars($option) . '</option>';
        }
        $html .= '</select>';
        return $html;
    }

    /**
     * Render a conditional checkbox with content.
     */
    protected function renderConditionalCheckbox($variableName, $blockContent)
    {
        $checked = $this->variables[$variableName] ?? false;

        // Set the flag to indicate we're inside a conditional block
        $this->inConditionalBlock = true;

        // Process the block content
        $processedContent = $this->processContent($blockContent);

        // Reset the flag
        $this->inConditionalBlock = false;

        // Render the checkbox with content
        $html = '<div class="conditional-block ">';
        $html .= '<label class=" flex items-baseline">';
        $html .= '<input type="checkbox" wire:model.live="variables.' . $variableName . '" class="-mb-2 mr-4 checkbox checkbox-xs" />';
        $html .= '<div>' . $processedContent . '</div>';
        $html .= '</label>';
        $html .= '</div>';

        return $html;
    }
 


    /* Livewire hook: called when any property is updated.
     */
    public function updated($propertyName)
    {
        // Do nothing; the content will be reprocessed on the next render
    }


    public function zoomIn()
    {
        $this->zoomLevel += 0.05;
    }

    public function zoomOut()
    {
        if ($this->zoomLevel > 0.2) {
            $this->zoomLevel -= 0.05;
        }
    }


    public function render()
    {
        return view('livewire.step03');
    }
}
