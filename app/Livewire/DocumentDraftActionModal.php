<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\DocumentDraft;

class DocumentDraftActionModal extends Component
{
    public $documentDraftData;

    public function mount($documentDraftData)
    {
        $this->documentDraftData = $documentDraftData;
    }

    public function continueDraft(){
        session(['step' => 2]);
        return redirect(url('/editor/step2'));
    }

    public function abandonDraft(){
        Log::info('Abandoning draft');
       // dd ($this->documentDraftData);
        $documentDraft = DocumentDraft::find($this->documentDraftData['id']);

        if ($documentDraft) {
            foreach (['partA', 'partB', 'partC'] as $relation) {
                if (isset($documentDraft->$relation)) {
                    $infoProfile = $documentDraft->$relation;
                    if ($infoProfile && $infoProfile->is_temp) {
                        $infoProfile->delete();
                    }
                }
            }
            $documentDraft->delete();
        }
        $this->documentDraftData = null;
        session(['step' => null]);
        return redirect(url('/editor'));
    }

    public function render()
    {
        return view('livewire.document-draft-action-modal');
    }
}
