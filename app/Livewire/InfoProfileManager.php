<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\InfoProfile;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class InfoProfileManager extends Component
{
    public $isModalOpen = true; // Always open when included in the parent component
    public $editMode = false;
    public $profileId = null;
    
    // Form fields
    public $entityType = 'individual';
    public $first_name, $last_name, $pronoun, $email, $phone, $date_of_birth, $place_of_birth, $county_of_birth;
    public $address_street, $address_number, $address_block, $address_entrance, $address_floor, $address_apt, $address_city, $address_county, $address_country;
    public $id_document_type, $id_document_series, $id_document_number, $id_document_issuer, $id_document_issue_date, $personal_id_number;
    public $bank_name, $bank_city, $iban;
    public $postal_code, $country_of_birth;
    public $company_name, $company_role, $tax_identification_number, $registration_number, $registration_county;
    
    protected function getListeners()
    {
        return [
            'openAddModal' => 'handleOpenAddModal',
            'openEditModal' => 'handleOpenEditModal',
        ];
    }
    
    public function mount()
    {
        $this->resetForm();
        
        // Check if we're in edit mode from the parent component
        if (request()->has('editProfileId')) {
            $this->handleOpenEditModal(request()->get('editProfileId'));
        }
    }
    
    public function handleOpenAddModal()
    {
        $this->resetForm();
        $this->editMode = false;
        $this->isModalOpen = true;
    }
    
    public function handleOpenEditModal($id)
    {
        $this->resetForm();
        $this->editMode = true;
        $this->profileId = $id;
        $this->loadProfile();
        $this->isModalOpen = true;
    }
    
    public function loadProfile()
    {
        $profile = InfoProfile::find($this->profileId);
        
        if ($profile) {
            $this->entityType = $profile->entity_type;
            $this->first_name = $profile->first_name;
            $this->last_name = $profile->last_name;
            $this->pronoun = $profile->pronoun;
            $this->email = $profile->email;
            $this->phone = $profile->phone;
            $this->date_of_birth = $profile->date_of_birth ? Carbon::parse($profile->date_of_birth)->toDateString() : null;
            $this->place_of_birth = $profile->place_of_birth;
            $this->county_of_birth = $profile->county_of_birth;
            $this->address_street = $profile->address_street;
            $this->address_number = $profile->address_number;
            $this->address_block = $profile->address_block;
            $this->address_entrance = $profile->address_entrance;
            $this->address_floor = $profile->address_floor;
            $this->address_apt = $profile->address_apt;
            $this->address_city = $profile->address_city;
            $this->address_county = $profile->address_county;
            $this->address_country = $profile->address_country;
            $this->id_document_type = $profile->id_document_type;
            $this->id_document_series = $profile->id_document_series;
            $this->id_document_number = $profile->id_document_number;
            $this->id_document_issuer = $profile->id_document_issuer;
            $this->id_document_issue_date = $profile->id_document_issue_date ? Carbon::parse($profile->id_document_issue_date)->toDateString() : null;
            $this->personal_id_number = $profile->personal_id_number;
            $this->bank_name = $profile->bank_name;
            $this->bank_city = $profile->bank_city;
            $this->iban = $profile->iban;
            $this->company_name = $profile->company_name;
            $this->tax_identification_number = $profile->tax_identification_number;
            $this->registration_number = $profile->registration_number;
            $this->registration_county = $profile->registration_county;
            $this->company_role = $profile->company_role;
            $this->postal_code = $profile->postal_code;
            $this->country_of_birth = $profile->country_of_birth;
        }
    }
    
    public function resetForm()
    {
        $this->entityType = 'individual';
        $this->first_name = null;
        $this->last_name = null;
        $this->pronoun = 'Dl.';
        $this->email = null;
        $this->phone = null;
        $this->date_of_birth = null;
        $this->place_of_birth = null;
        $this->county_of_birth = null;
        $this->address_street = null;
        $this->address_number = null;
        $this->address_block = null;
        $this->address_entrance = null;
        $this->address_floor = null;
        $this->address_apt = null;
        $this->address_city = null;
        $this->address_county = null;
        $this->address_country = null;
        $this->id_document_type = 'CI';
        $this->id_document_series = null;
        $this->id_document_number = null;
        $this->id_document_issuer = null;
        $this->id_document_issue_date = null;
        $this->personal_id_number = null;
        $this->bank_name = null;
        $this->bank_city = null;
        $this->iban = null;
        $this->company_name = null;
        $this->tax_identification_number = null;
        $this->registration_number = null;
        $this->registration_county = null;
        $this->company_role = null;
        $this->postal_code = null;
        $this->country_of_birth = null;
        $this->profileId = null;
    }
    
    public function save($type)
    {
        $data = [
            'entity_type' => $type,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'date_of_birth' => $this->date_of_birth,
            'place_of_birth' => $this->place_of_birth,
            'county_of_birth' => $this->county_of_birth,
            'address_street' => $this->address_street,
            'address_number' => $this->address_number,
            'address_block' => $this->address_block,
            'address_entrance' => $this->address_entrance,
            'address_floor' => $this->address_floor,
            'address_apt' => $this->address_apt,
            'address_city' => $this->address_city,
            'address_county' => $this->address_county,
            'address_country' => $this->address_country,
            'tax_identification_number' => $this->tax_identification_number,
            'registration_number' => $this->registration_number,
            'registration_county' => $this->registration_county,
            'postal_code' => $this->postal_code,
            'country_of_birth' => $this->country_of_birth,
            'pronoun' => $this->pronoun,
            'id_document_type' => $this->id_document_type,
            'id_document_series' => $this->id_document_series,
            'id_document_number' => $this->id_document_number,
            'id_document_issuer' => $this->id_document_issuer,
            'id_document_issue_date' => $this->id_document_issue_date,
            'personal_id_number' => $this->personal_id_number,
            'bank_name' => $this->bank_name,
            'bank_city' => $this->bank_city,
            'iban' => $this->iban,
            'company_name' => $this->company_name,
            'company_role' => $this->company_role,
        ];
        
        if ($this->editMode && $this->profileId) {
            $profile = InfoProfile::find($this->profileId);
            if ($profile && $profile->user_id == Auth::id()) {
                $profile->update($data);
            }
        } else {
            InfoProfile::create($data + ['user_id' => Auth::id()]);
        }
        
        $this->dispatch('profileSaved');
    }
    
    public function cancel()
    {
        $this->dispatch('profileSaved');
    }
    
    public function render()
    {
        return view('livewire.info-profile-manager');
    }
}
