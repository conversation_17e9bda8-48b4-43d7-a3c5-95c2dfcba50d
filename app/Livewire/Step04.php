<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Document;
use App\Models\Template;
use App\Services\CreditService;
use Illuminate\Support\Facades\Auth;

class Step04 extends Component
{
    public $document_pdf_url;
    public $document_preview_url;
    public $template;
    public $user;
    public $can_download;
    public $needs_credits;
    public $template_credit_cost = 50;
    
    protected $creditService;

    public function boot(CreditService $creditService)
    {
        $this->creditService = $creditService;
    }
    
    public function mount()
    {
        $document = Document::where('user_id', Auth::id())
            ->where('status', 'generated')
            ->latest()
            ->first();
        $this->template = Template::find($document->template_id);
        $this->document_pdf_url = session('document_pdf_url');
        $this->document_preview_url = session('document_preview_url');
        
        $this->user = Auth::user();
        
        $this->can_download = $this->canUserDownload();
        $this->needs_credits = ($this->template->id > 1);
    }

    protected function canUserDownload(): bool
    {
        if ($this->template->id <= 1) {
            return true;
        }

        return $this->creditService->getBalance($this->user) >= $this->template_credit_cost;
    }

    public function downloadDocument()
    {
        if (!$this->can_download) {
            return redirect()->route('buy-credits');
        }

        if ($this->template->id > 1) {
            $success = $this->creditService->deductCredits(
                $this->user, 
                $this->template_credit_cost,
                'Document download: ' . $this->template->name
            );

            if (!$success) {
                session()->flash('error', 'Insufficient credits');
                return redirect()->route('buy-credits');
            }
            
            // Mark document as paid for
            $document = Document::where('user_id', Auth::id())
                ->where('status', 'generated')
                ->latest()
                ->first();
                
            if ($document) {
                $document->was_paid_for = true;
                $document->save();
            }
        }

        $filePath = str_replace('/storage/', '', parse_url($this->document_pdf_url, PHP_URL_PATH));
        
        // Get the response for download
        $response = response()->download(storage_path('app/public/' . $filePath));
        
        // Delete the user's document draft
        $draft = \App\Models\DocumentDraft::where('user_id', Auth::id())->first();
        if ($draft) {
            $draft->delete();
        }
        
        // Clear session variables related to the document
        session()->forget(['document_pdf_url', 'document_preview_url', 'step']);
        
        // Set a flag to show the success modal after download
        $this->dispatch('documentDownloaded');
        
        return $response;
    }

    public function redirectToDashboard()
    {
        return redirect()->route('dashboard');
    }

    public function render()
    {
        return view('livewire.step04');
    }

    
}
