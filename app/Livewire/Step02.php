<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\DocumentDraft;
use App\Models\InfoProfile;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Broadcast;
//use App\Events\TestEvent;
use App\Events\PrivateTestEvent;
use Illuminate\Support\Facades\Auth;

class Step02 extends Component
{
    public $documentDraft;
    public $part_a;
    public $part_b;
    public $part_c;
    public $slots;
    public $infoProfiles;
    public $dataComplete;
    public $documentCategory;
    public $infoProfileIdA;
    public $infoProfileIdB;
    public $infoProfileIdC;
    public $isModalOpen = false;
    public $modalSelected = '';
    public $userInfoProfiles = [];
    public $selectedProfileA;
    public $selectedProfileB;
    public $selectedProfileC;

    protected $listeners = [
        'infoProfileSaved' => 'infoProfileSaved',
        'infoProfileCanceled' => 'infoProfileCanceled'
    ];

  //  #[On('trigger')]
  //  public function received()
  //  {
  //      dd('Event received!');
   // }

    public function infoProfileCanceled(){
        $this->isModalOpen = false;
        $this->modalSelected = '';
    }

    public function infoProfileSaved($payload)
    {
     $this->isModalOpen = false;
     $this->modalSelected = '';
   // $draft_parts = [
        
    //];

    //$draft_parts[$payload['part']] = $payload['infoProfileId'];

    $this->documentDraft->update([
        $payload['part'] => $payload['infoProfileId']
    ]);
    
    if ($this->slots == 1 && $this->documentDraft->part_a) {
        $this->dispatch('enableNext');
    } elseif ($this->slots == 2 && $this->documentDraft->part_a && $this->documentDraft->part_b) {
        $this->dispatch('enableNext');
    } elseif ($this->slots == 3 && $this->documentDraft->part_a && $this->documentDraft->part_b && $this->documentDraft->part_c) {
        $this->dispatch('enableNext');
    }
    // $this->trigger('Info Profile Saved');
    }

    public function trigger($message){
   //     $id = Auth::id();
   //     broadcast(new PrivateTestEvent($id,$message));
    }

    public function mount()
    {
        $this->documentDraft = DocumentDraft::where('user_id', auth()->id())->first();
        Log::info($this->documentDraft);
        $this->documentCategory = $this->documentDraft->template->category->id;

        if (in_array($this->documentCategory, [1, 3])) {
            $this->slots = 1;
        } elseif (in_array($this->documentCategory, [2, 4])) {
            $this->slots = 2;
        } else {
            $this->slots = 0; // Default case if needed
        }

 
            $this->infoProfileIdA = $this->documentDraft->part_a;
            $this->infoProfileIdB = $this->documentDraft->part_b;
            $this->infoProfileIdC = $this->documentDraft->part_c;
            Log::info($this->infoProfileIdA);  
        
            $this->loadUserInfoProfiles();
        
        $this->selectedProfileA = $this->infoProfileIdA;
        $this->selectedProfileB = $this->infoProfileIdB;
        $this->selectedProfileC = $this->infoProfileIdC;
   
    }

    public function loadUserInfoProfiles()
    {
        $this->userInfoProfiles = InfoProfile::where('user_id', auth()->id())->get();
    }

    public function saveSelectedProfile($part)
    {
         $selectedProfileVar = 'selectedProfile' . strtoupper($part[strlen($part)-1]); 
         $selectedId = $this->{$selectedProfileVar};
         if ($selectedId) {
            $this->documentDraft->update([
                $part => $selectedId
            ]);

                //update the infoProfileId
                $infoProfileIdVar = 'infoProfileId' . strtoupper($part[strlen($part)-1]); 
                $this->{$infoProfileIdVar} = $selectedId;
                }

            //  Store the ID in the DocumentDraft
            //    $this->documentDraft->{$this->part} = $this->infoProfile->id;
            //    $this->documentDraft->save();
                

                Log::info('Dispatching infoProfileSaved event', [
                    'infoProfileId' => $selectedId,
                    'part' => $part,
                ]);
                
                // Dispatch event with the infoProfileId and the part
                $this->dispatch('infoProfileSaved', [
                    'infoProfileId' => $selectedId,
                    'part' => $part,
                ]);

    }
    
    public function refreshProfiles($infoProfileId)
        {
            // Optionally, you can fetch the new profile details
            $newProfile = InfoProfile::find($infoProfileId);
    
            if ($newProfile) {
                if ($newProfile->is_client) {
                    $this->clientProfiles->push($newProfile);
                } else {
                    $this->nonClientProfiles->push($newProfile);
                }
            }
    
            // Optional: Flash message
            session()->flash('success', 'New InfoProfile added successfully.');
        }

   // public function infoProfileSaved(){
   //     
  //  }


    public function openModalA()
    {
        $this->modalSelected = 'A';
        $this->isModalOpen = true;
        //$this->emit('openModalA');
    }

    public function openModalB()
    {
        $this->modalSelected = 'B';
        $this->isModalOpen = true;
        //$this->emit('openModalB');
    }

    public function openModalC()
    {
        $this->modalSelected = 'C';
        $this->isModalOpen = true;
        //$this->emit('openModalC');
    }

    public function closeModal()
    {
        $this->modalSelected = null;
        $this->dispatch('refresh');
    }

    public function render()
    {
        return view('livewire.step02');
    }
}
