<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Services\CreditService;

class CreditBalanceSimple extends Component
{
    public $balance = 0;

    public function mount()
    {
        $creditService = app(CreditService::class);
        $this->balance = $creditService->getBalance(Auth::user());
    }

    public function render()
    {
        return view('livewire.credit-balance-simple');
    }
}