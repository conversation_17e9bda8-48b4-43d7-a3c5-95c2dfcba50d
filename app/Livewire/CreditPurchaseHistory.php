<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\CreditTransaction;

class CreditPurchaseHistory extends Component
{
    public function render()
    {
        $transactions = CreditTransaction::where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('livewire.credit-purchase-history', [
            'transactions' => $transactions
        ]);
    }
}