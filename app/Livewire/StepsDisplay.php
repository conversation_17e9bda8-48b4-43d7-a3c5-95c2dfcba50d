<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Log;

class StepsDisplay extends Component
{

    public $step = 1;

    public function mount()
    {
        Log::info('Step: ' . session()->get('step'));
        if (session()->has('step')) {
       //     dd(session('step'));
            $this->step = session('step');
        }

    }

    protected $listeners = ['stepChanged' => 'updateStep'];

    public function updateStep($step)
    {
        $this->step = $step;
    }

    public function render()
    {
        return view('livewire.steps-display');
    }
}
