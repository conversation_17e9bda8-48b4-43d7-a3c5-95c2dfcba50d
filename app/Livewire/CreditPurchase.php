<?php

namespace App\Livewire;

use Livewire\Component;
use App\Services\CreditService;

class CreditPurchase extends Component
{
    protected $creditService;

    public function boot(CreditService $creditService)
    {
        $this->creditService = $creditService;
    }

    public function purchaseCredits($amount)
    {
        // Here you would typically integrate with your payment provider
        // For now, we'll just add the credits directly
        $this->creditService->addCredits(
            auth()->user(),
            $amount,
            "Credit purchase"
        );

        $this->dispatch('credit-balance-updated');
        
        session()->flash('message', 'Credits purchased successfully!');
    }

    public function render()
    {
        return view('livewire.credit-purchase');
    }
}