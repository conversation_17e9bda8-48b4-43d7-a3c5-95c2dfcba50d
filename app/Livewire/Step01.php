<?php

namespace App\Livewire;


use Livewire\Component;
use App\Models\DocumentDraft;
use App\Models\InfoProfile;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Broadcast;
//use App\Events\TestEvent;
use App\Events\PrivateTestEvent;
use Illuminate\Support\Facades\Auth;
use App\Models\DocumentCategory;
use App\Models\DocumentSubcategory;
use App\Models\Template;


class Step01 extends Component
{
    public $categories;
    public $subcategories = [];
    public $templates = [];
    public $selectedCategory;
    public $selectedCategoryName;
    public $selectedSubcategory;
    public $selectedTemplate;
    public $step;
    
    // New properties for confirmation
    public $confirmedCategory = null;
    public $confirmedSubcategory = null;
    
    public $documentDraft;
    public $part_a;
    public $part_b;
    public $part_c;
    public $slots;
    public $infoProfiles;
    public $dataComplete;
    public $documentCategory;
    public $infoProfileIdA;
    public $infoProfileIdB;
    public $infoProfileIdC;
    public $isModalOpen = false;
    public $modalSelected = '';
    public $templateId;

    protected $listeners = ['infoProfileSaved' => 'infoProfileSaved', 'goNext' => 'nextStep', 'goBack' => 'backStep'];



    public function mount()
    {
        $this->initialize();
    }

    public function initialize()
    {
        $this->categories = DocumentCategory::with('documentSubcategories')->get();
    }

    public function confirmCategory()
    {
        if (!empty($this->selectedCategory)) {
            $this->confirmedCategory = $this->selectedCategory;
            $category = $this->categories->firstWhere('id', $this->selectedCategory);
            if ($category) {
                $this->subcategories = $category->documentSubcategories;
                $this->selectedCategoryName = $category->name;
            }
        }
    }

    public function confirmSubcategory()
    {
        if (!empty($this->selectedSubcategory)) {
            $this->confirmedSubcategory = $this->selectedSubcategory;
            $this->templates = Template::where('subcategory_id', $this->selectedSubcategory)->get();
        }
    }
    public function updatedSelectedTemplate($templateId)
    {
        if (!empty($templateId)) {
            $this->templateId = $templateId;
            $this->dispatch('enableNextStep', ['templateId' => $templateId]);
        }
    }
    public function showNextStepButton(){
        
    }

    public function backStep($step){
       
      
        $this->step = session('step');
        $this->initialize();
      //  $this->step = $step;
     //   session(['step' => $this->step]);
       
    }


    public function nextStep()
    {
        $draft = DocumentDraft::where('user_id', Auth::id())->first();
        
        if (!$draft) {
            $draft = DocumentDraft::create([
                'user_id' => Auth::id(),
                'template_id' => $this->templateId,
                'current_step' => 1
            ]);
        }
        
        $draft->template_id = $this->templateId;
        $draft->current_step = 2;
        $draft->save();

        session(['step' => 2]);
        return redirect('/editor/step2');
    }

    public function resetSelections()
    {
        $this->selectedCategory = null;
        $this->selectedSubcategory = null;
        $this->selectedTemplate = null;
        $this->confirmedCategory = null;
        $this->confirmedSubcategory = null;
        $this->subcategories = [];
        $this->templates = [];
        $this->selectedCategoryName = null;
    }
    public function render()
    {
        return view('livewire.step01');
    }
}
