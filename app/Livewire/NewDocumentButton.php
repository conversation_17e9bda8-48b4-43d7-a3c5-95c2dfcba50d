<?php

namespace App\Livewire;

use App\Models\DocumentDraft;
use App\Models\Template;
use Livewire\Component;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class NewDocumentButton extends Component
{
    public $userDraft;
    public $draftObject;
    public $modalId;
    public $templateName;
    public $creationDate;

    public function mount()
    {
        // Generate a unique ID for this instance of the modal
        $this->modalId = 'document_draft_prompt_' . Str::random(8);
        
        $this->userDraft = DocumentDraft::where('user_id', auth()->id())->exists();
        $this->draftObject = DocumentDraft::where('user_id', auth()->id())->first();
        
        if ($this->draftObject) {
            // Get template name
            $template = Template::find($this->draftObject->template_id);
            $this->templateName = $template ? $template->name : 'necunoscut';
            
            // Format creation date
            $this->creationDate = $this->draftObject->created_at->format('d.m.Y, H:i');
        }
    }

    public function toEditor()
    {
        Log::info('Draft does not exist, redirecting to editor');
        return redirect()->to('/editor');
    }

    public function continueDraft()
    {
        Log::info('Draft exists, continuing draft');
        $step = $this->draftObject->current_step ?? 2;
        return redirect()->to("/editor/step{$step}");
    }

    public function abandonDraft()
    {
        Log::info('Draft exists, abandoning draft');
        $this->draftObject->delete();
        return redirect()->to('/editor');
    }
    
    public function render()
    {
        return view('livewire.new-document-button');
    }
}
