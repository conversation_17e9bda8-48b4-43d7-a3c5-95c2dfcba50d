<?php

namespace App\Services;

use App\Models\User;
use App\Models\CreditTransaction;
use Illuminate\Support\Facades\DB;

class CreditService
{
    public function addCredits(User $user, float $amount, string $description = null): void
    {
        $user->increaseCredit($amount);
        
        CreditTransaction::create([
            'user_id' => $user->id,
            'amount' => $amount,
            'description' => $description ?? 'Credit purchase',
            'balance_after' => $user->credit
        ]);
    }

    public function deductCredits(User $user, float $amount, string $description = null): bool
    {
        if (!$user->hasCredit() || $user->credit < $amount) {
            return false;
        }

        $user->decreaseCredit($amount);
        
        CreditTransaction::create([
            'user_id' => $user->id,
            'amount' => -$amount,
            'description' => $description ?? 'Document generation',
            'balance_after' => $user->credit
        ]);

        return true;
    }

    public function grantSubscriptionCredits(User $user, float $amount): void
    {
        $user->increaseCredit($amount);
        
        CreditTransaction::create([
            'user_id' => $user->id,
            'amount' => $amount,
            'description' => 'Subscription credit grant',
            'balance_after' => $user->credit
        ]);
    }

    public function getBalance(User $user): float
    {
        return (float) ($user->credit ?? 0.0);
    }

    public function initializeBalance(User $user, float $amount = 0.0): void
    {
        if (!$user->hasCredit()) {
            $user->setCredit($amount);
        }
    }

    public function getTransactionHistory(User $user, int $limit = 10)
    {
        return $user->credits()
            ->latest()
            ->limit($limit)
            ->get();
    }
}
