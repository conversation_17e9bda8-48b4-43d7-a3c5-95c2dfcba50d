<?php

namespace App\Providers;

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Route;
use Laravel\Sanctum\Sanctum;


class BroadcastServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Broadcast::routes();
      // Broadcast::routes(['middleware' => ['auth:api']]);
     // Broadcast::routes(['middleware' => ['web', 'auth:client']]);
      Broadcast::routes(['middleware' => ['web']]);
        require base_path('routes/channels.php');
    }
}
