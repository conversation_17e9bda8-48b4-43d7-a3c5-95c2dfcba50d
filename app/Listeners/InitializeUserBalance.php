<?php

namespace App\Listeners;

use App\Services\CreditService;
use Illuminate\Auth\Events\Registered;

class InitializeUserBalance
{
    protected $creditService;

    public function __construct(CreditService $creditService)
    {
        $this->creditService = $creditService;
    }

    public function handle(Registered $event): void
    {
        $this->creditService->initializeBalance($event->user);
    }
}