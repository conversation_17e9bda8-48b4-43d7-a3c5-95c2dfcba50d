<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\DocumentDraft;

class EditorStepMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $user = auth()->user();
        if (!$user) {
            return redirect()->route('login');
        }

        // Handle draft abandonment
        if ($request->has('abandon')) {
            $draft = DocumentDraft::where('user_id', $user->id)->first();
            session(['step' => null]);
            if ($draft) {
                $draft->delete();
            }
            return redirect('/editor');
        }

        $draft = DocumentDraft::where('user_id', $user->id)->first();
        $currentPath = $request->path();
        $step = null;

        // Extract step number from URL if it exists
        if (preg_match('/editor\/step(\d+)/', $currentPath, $matches)) {
            $step = (int)$matches[1];
        } elseif ($currentPath === 'editor') {
            $step = 1;
        }

        // If no draft exists and trying to access steps > 1, redirect to editor
        if (!$draft && $step > 1) {
            return redirect('/editor');
        }

        // If draft exists, ensure user is on the correct step
        if ($draft) {
            $correctStep = $draft->current_step ?? 1;
            
            // If trying to access a different step, redirect to the correct one
            if ($step && $step !== $correctStep) {
                // If it's step 1, redirect to /editor instead of /editor/step1
                return $correctStep === 1 
                    ? redirect('/editor')
                    : redirect("/editor/step{$correctStep}");
            }
            
            // Store step in session
            session(['step' => $correctStep]);
        }

        return $next($request);
    }
}
