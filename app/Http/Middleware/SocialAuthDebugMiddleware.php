<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SocialAuthDebugMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // Log all social auth requests
        if (str_contains($request->path(), 'auth/') && (str_contains($request->path(), '/callback') || str_contains($request->path(), '/redirect'))) {
            Log::channel('social_auth')->info('Social Auth Request', [
                'path' => $request->path(),
                'method' => $request->method(),
                'query_params' => $request->query(),
                'headers' => $request->headers->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);
        }

        try {
            $response = $next($request);
            
            // Log successful responses
            if (str_contains($request->path(), 'auth/') && (str_contains($request->path(), '/callback') || str_contains($request->path(), '/redirect'))) {
                Log::channel('social_auth')->info('Social Auth Response', [
                    'path' => $request->path(),
                    'status_code' => $response->getStatusCode(),
                    'headers' => $response->headers->all(),
                ]);
            }
            
            return $response;
        } catch (\Exception $e) {
            // Log any exceptions
            Log::channel('social_auth')->error('Social Auth Exception', [
                'path' => $request->path(),
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }
}
