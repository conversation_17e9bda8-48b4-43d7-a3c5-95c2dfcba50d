<?php

namespace App\Http\Controllers;

use App\Models\Document;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class DocumentController extends Controller
{


    /**
     * Delete a document
     */
    public function delete(Document $document)
    {
        // Check if the document belongs to the authenticated user
        if ($document->user_id !== Auth::id()) {
            return redirect()->back()->with('error', 'Nu ai permisiunea să ștergi acest document.');
        }

        // Get file paths
        $pdfPath = str_replace('/storage/', '', parse_url($document->document_url, PHP_URL_PATH));
        $thumbnailPath = str_replace('/storage/', '', parse_url($document->thumbnail, PHP_URL_PATH));

        // Delete files from storage
        if ($pdfPath) {
            Storage::disk('public')->delete($pdfPath);
        }
        
        if ($thumbnailPath) {
            Storage::disk('public')->delete($thumbnailPath);
        }

        // Delete the document record
        $document->delete();

        // Redirect back with success message
        return redirect()->back()->with('success', 'Documentul a fost șters cu succes.');
    }
}