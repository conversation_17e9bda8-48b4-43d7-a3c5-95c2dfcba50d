<?php

namespace App\Http\Controllers;

use Devdojo\Auth\Http\Controllers\SocialController as BaseSocialController;
use Illuminate\Support\Facades\Request;
use Illuminate\Http\Request as HttpRequest;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Auth;

class DebugSocialController extends BaseSocialController
{
    private function debugLog($message, $data = [])
    {
        $logEntry = [
            'timestamp' => now()->toDateTimeString(),
            'message' => $message,
            'data' => $data
        ];
        
        file_put_contents(
            storage_path('logs/social_debug.log'), 
            json_encode($logEntry, JSON_PRETTY_PRINT) . "\n\n", 
            FILE_APPEND | LOCK_EX
        );
    }

    public function callback($driver)
    {
        $this->debugLog('Social callback started', [
            'driver' => $driver,
            'query_params' => Request::query(),
            'url' => Request::fullUrl()
        ]);

        try {
            $this->debugLog('Setting up credentials for driver: ' . $driver);
            $this->dynamicallySetSocialProviderCredentials($driver);
            
            $this->debugLog('Credentials set, attempting to get user from ' . $driver);
            $socialiteUser = Socialite::driver($driver)->user();
            
            $this->debugLog('Socialite user retrieved', [
                'id' => $socialiteUser->getId(),
                'email' => $socialiteUser->getEmail(),
                'name' => $socialiteUser->getName(),
                'nickname' => $socialiteUser->getNickname(),
            ]);

            $this->debugLog('Finding or creating provider user');
            $providerUser = $this->findOrCreateProviderUser($socialiteUser, $driver);

            if ($providerUser instanceof \Illuminate\Http\RedirectResponse) {
                $this->debugLog('Provider user returned redirect response');
                return $providerUser;
            }

            $this->debugLog('Logging in user', [
                'user_id' => $providerUser->user->id,
                'user_email' => $providerUser->user->email
            ]);
            
            Auth::login($providerUser->user);

            $this->debugLog('User logged in successfully, redirecting');
            return redirect()->to(config('devdojo.auth.settings.redirect_after_auth'));
            
        } catch (\Exception $e) {
            $this->debugLog('Exception caught in callback', [
                'exception_class' => get_class($e),
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('auth.login')->with('error', 'An error occurred during authentication. Please try again.');
        }
    }
}
