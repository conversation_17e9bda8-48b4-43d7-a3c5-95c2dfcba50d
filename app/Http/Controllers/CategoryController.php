<?php

namespace App\Http\Controllers;

use App\Models\DocumentCategory;
use App\Models\Document;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CategoryController extends Controller
{
    public function show($slug)
    {
        // Get the category from the slug
        $category = DocumentCategory::where('slug', $slug)->firstOrFail();

        // Get documents for this user and category
        $documents = Document::where('user_id', Auth::id())
            ->where('category_id', $category->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('themes.anchor.pages.arhiva.category', [
            'category' => $category,
            'documents' => $documents,
        ]);
    }
}