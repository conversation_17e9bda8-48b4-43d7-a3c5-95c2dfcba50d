<?php

namespace App\Http\Controllers;

use App\Models\Pdfable;
use Illuminate\Http\Request;

class PdfableController extends Controller
{
    public function generate(Pdfable $pdfable)
    {
        try {
        return view('designs.default', [
            'templateTitle' => $pdfable->template_title,
            'finalContent' => $pdfable->content,
            'partyA_rep_name' => $pdfable->party_a_rep_name,
            'partyA_rep_role' => $pdfable->party_a_rep_role,
            'partyB_rep_name' => $pdfable->party_b_rep_name,
            'partyB_rep_role' => $pdfable->party_b_rep_role,
            'document_category' => $pdfable->document_category,
        ]);
        } catch (\Exception $e) {
            Log::error('PDF Generation View Error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}