<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\NewTemplate;
use App\Models\DocumentCategory;
use App\Models\DocumentSubcategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NewTemplateController extends Controller
{
    /**
     * Create a new template from API request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

// Retrieve API key from headers
    $apiKey = $request->header('X-API-KEY');

    // Verify API key (ensure your config file contains 'services.template_api.key')
    if (!$apiKey || $apiKey !== config('services.template_api.key')) {
        return response()->json([
            'success' => false,
            'message' => 'Invalid API key'
        ], 401);
    }



        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'content' => 'required|string',
            'category_id' => 'required|exists:document_categories,id',
            'subcategory_id' => 'required|exists:document_subcategories,id',
            'tier_id' => 'required|exists:template_tiers,id',
           // 'api_key' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Verify API key (you should implement a more secure method)
        
        try {
            // Create the new template
            $template = NewTemplate::create([
                'name' => $request->name,
                'description' => $request->description,
                'content' => $request->content,
                'category_id' => $request->category_id,
                'subcategory_id' => $request->subcategory_id,
                'tier_id' => $request->tier_id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Template created successfully',
                'data' => $template
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create template',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
