<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\Template;
use App\Services\CreditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class DocumentPaymentController extends Controller
{
    protected $creditService;
    
    public function __construct(CreditService $creditService)
    {
        $this->creditService = $creditService;
    }
    
    public function show(Document $document)
    {
        $template = Template::find($document->template_id);
        $user = Auth::user();
        $template_credit_cost = 50; // You might want to make this configurable
        $can_download = $this->canUserDownload($template, $document, $user, $template_credit_cost);
        $needs_credits = ($template->tier_id > 1 && !$document->was_paid_for);
        
        return view('documents.payment', compact(
            'document',
            'template',
            'user',
            'can_download',
            'needs_credits',
            'template_credit_cost'
        ));
    }
    
    public function download(Document $document)
    {
        $template = Template::find($document->template_id);
        $user = Auth::user();
        $template_credit_cost = 50; // You might want to make this configurable
        
        // Free tier documents or already paid documents
        if ($template->tier_id <= 1 || $document->was_paid_for) {
            return $this->processDownload($document);
        }
        
        // Check if user has enough credits
        if ($this->creditService->getBalance($user) < $template_credit_cost) {
            return redirect()->route('buy-credits')->with('error', 'Insufficient credits');
        }
        
        // Deduct credits
        $success = $this->creditService->deductCredits(
            $user, 
            $template_credit_cost,
            'Document download: ' . $template->name
        );
        
        if (!$success) {
            return redirect()->route('buy-credits')->with('error', 'Failed to process payment');
        }
        
        // Mark as paid
        $document->was_paid_for = true;
        $document->save();
        
        return $this->processDownload($document);
    }
    
    protected function processDownload(Document $document)
    {
        $filePath = str_replace('/storage/', '', parse_url($document->document_url, PHP_URL_PATH));
        return response()->download(storage_path('app/public/' . $filePath));
    }
    
    protected function canUserDownload($template, $document, $user, $cost): bool
    {
        if ($template->tier_id <= 1 || $document->was_paid_for) {
            return true;
        }

        return $this->creditService->getBalance($user) >= $cost;
    }
}