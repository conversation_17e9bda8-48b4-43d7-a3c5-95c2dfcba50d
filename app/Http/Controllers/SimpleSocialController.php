<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Request;
use Devdojo\Auth\Models\SocialProvider;
use Devdojo\Auth\Models\SocialProviderUser;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Laravel\Socialite\Facades\Socialite;

class SimpleSocialController extends Controller
{
    private function debugLog($message, $data = [])
    {
        $logEntry = [
            'timestamp' => now()->toDateTimeString(),
            'message' => $message,
            'data' => $data
        ];

        file_put_contents(
            storage_path('logs/simple_social_debug.log'),
            json_encode($logEntry, JSON_PRETTY_PRINT) . "\n\n",
            FILE_APPEND | LOCK_EX
        );
    }

    public function redirect($driver)
    {
        $this->debugLog('Social redirect started', ['driver' => $driver]);

        try {
            $this->dynamicallySetSocialProviderCredentials($driver);
            $this->debugLog('Credentials set, redirecting to ' . $driver);
            return Socialite::driver($driver)->redirect();
        } catch (\Exception $e) {
            $this->debugLog('Redirect error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }
    }

    public function callback($driver)
    {
        $this->debugLog('Social callback started', [
            'driver' => $driver,
            'query_params' => Request::query(),
            'url' => Request::fullUrl(),
            'method' => Request::method(),
            'all_params' => Request::all(),
        ]);

        try {
            $this->debugLog('Setting up credentials for driver: ' . $driver);
            $this->dynamicallySetSocialProviderCredentials($driver);

            $this->debugLog('Credentials set, attempting to get user from ' . $driver);
            $socialiteUser = Socialite::driver($driver)->user();

            $this->debugLog('Socialite user retrieved', [
                'id' => $socialiteUser->getId(),
                'email' => $socialiteUser->getEmail(),
                'name' => $socialiteUser->getName(),
                'nickname' => $socialiteUser->getNickname(),
            ]);

            $this->debugLog('Finding or creating provider user');
            $providerUser = $this->findOrCreateProviderUser($socialiteUser, $driver);

            if ($providerUser instanceof \Illuminate\Http\RedirectResponse) {
                $this->debugLog('Provider user returned redirect response');
                return $providerUser;
            }

            $this->debugLog('Logging in user', [
                'user_id' => $providerUser->user->id,
                'user_email' => $providerUser->user->email
            ]);

            Auth::login($providerUser->user);

            $this->debugLog('User logged in successfully, redirecting to dashboard');
            return redirect()->to('/dashboard');

        } catch (\Exception $e) {
            $this->debugLog('Exception caught in callback', [
                'exception_class' => get_class($e),
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('auth.login')->with('error', 'Debug: ' . $e->getMessage());
        }
    }

    private function dynamicallySetSocialProviderCredentials($provider)
    {
        $socialProvider = $this->getProviderCredentialsWithOverrides($provider);

        Config::set('services.'.$provider.'.client_id', $socialProvider->client_id);
        Config::set('services.'.$provider.'.client_secret', $socialProvider->client_secret);
        Config::set('services.'.$provider.'.redirect', '/auth/'.$provider.'/callback');
    }

    private function getProviderCredentialsWithOverrides($provider)
    {
        $socialProvider = SocialProvider::where('slug', $provider)->first();

        switch ($provider) {
            case 'facebook':
                $socialProvider->client_id = sprintf('%d', $socialProvider->client_id);
                break;
        }

        return $socialProvider;
    }

    private function findOrCreateProviderUser($socialiteUser, $driver)
    {
        $this->debugLog('Looking for existing provider user', [
            'provider_slug' => $driver,
            'provider_user_id' => $socialiteUser->getId()
        ]);

        $providerUser = SocialProviderUser::where('provider_slug', $driver)
            ->where('provider_user_id', $socialiteUser->getId())
            ->first();

        if ($providerUser) {
            $this->debugLog('Found existing provider user', ['id' => $providerUser->id]);
            return $providerUser;
        }

        $this->debugLog('No existing provider user found, checking for user by email');
        $user = app(config('auth.providers.users.model'))->where('email', $socialiteUser->getEmail())->first();

        if ($user) {
            $this->debugLog('Found existing user by email', ['user_id' => $user->id]);
            $existingProvider = $user->socialProviders()->first();
            if ($existingProvider) {
                $this->debugLog('User already has social provider', [
                    'existing_provider' => $existingProvider->provider_slug
                ]);
                return redirect()->route('auth.login')->with('error',
                    "This email is already associated with a {$existingProvider->provider_slug} account. Please login using that provider.");
            }
        }

        $this->debugLog('Creating new user and social provider user');
        return DB::transaction(function () use ($socialiteUser, $driver, $user) {
            $user = $user ?? $this->createUser($socialiteUser);
            return $this->createSocialProviderUser($user, $socialiteUser, $driver);
        });
    }

    private function createUser($socialiteUser)
    {
        // Generate a username from the email
        $username = $this->generateUsernameFromEmail($socialiteUser->getEmail());

        $this->debugLog('Creating new user', [
            'name' => $socialiteUser->getName(),
            'email' => $socialiteUser->getEmail(),
            'username' => $username
        ]);

        return app(config('auth.providers.users.model'))->create([
            'name' => $socialiteUser->getName(),
            'email' => $socialiteUser->getEmail(),
            'username' => $username,
            'email_verified_at' => now(),
        ]);
    }

    private function generateUsernameFromEmail($email)
    {
        // Get the part before @ and clean it up
        $baseUsername = strtolower(explode('@', $email)[0]);

        // Remove any non-alphanumeric characters except underscores and hyphens
        $baseUsername = preg_replace('/[^a-z0-9_-]/', '', $baseUsername);

        // Ensure it's not empty
        if (empty($baseUsername)) {
            $baseUsername = 'user';
        }

        // Check if username already exists and add a number if needed
        $username = $baseUsername;
        $counter = 1;

        while (app(config('auth.providers.users.model'))->where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }

    private function createSocialProviderUser($user, $socialiteUser, $driver)
    {
        $this->debugLog('Creating social provider user', [
            'user_id' => $user->id,
            'provider_slug' => $driver,
            'provider_user_id' => $socialiteUser->getId()
        ]);

        return $user->socialProviders()->create([
            'provider_slug' => $driver,
            'provider_user_id' => $socialiteUser->getId(),
            'nickname' => $socialiteUser->getNickname(),
            'name' => $socialiteUser->getName(),
            'email' => $socialiteUser->getEmail(),
            'avatar' => $socialiteUser->getAvatar(),
            'provider_data' => json_encode($socialiteUser->user),
            'token' => $socialiteUser->token,
            'refresh_token' => $socialiteUser->refreshToken,
            'token_expires_at' => $socialiteUser->expiresIn ? now()->addSeconds($socialiteUser->expiresIn) : null,
        ]);
    }
}
