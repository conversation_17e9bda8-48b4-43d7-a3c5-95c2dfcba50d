<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DocumentDraft extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'template_id',
        'current_step',  // Add this
        'part_a',
        'part_b',
        'part_c',
        'variables',
        'content',
        'final_content',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'template_id' => 'integer',
        'part_a' => 'integer',
        'part_b' => 'integer',
        'part_c' => 'integer',
        'variables' => 'array',
        
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }

    public function partA(): BelongsTo
    {
        return $this->belongsTo(InfoProfile::class);
    }

    public function partB(): BelongsTo
    {
        return $this->belongsTo(InfoProfile::class);
    }

    public function partC(): BelongsTo
    {
        return $this->belongsTo(InfoProfile::class);
    }
}
