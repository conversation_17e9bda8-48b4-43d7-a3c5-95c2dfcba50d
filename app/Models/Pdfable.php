<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Pdfable extends Model
{
    use HasUuids;

    protected $fillable = [
        'template_title',
        'content',
        'party_a_rep_name',
        'party_a_rep_role',
        'party_b_rep_name',
        'party_b_rep_role',
        'document_category',
        'user_id'
    ];

    protected $casts = [
        'document_category' => 'integer',
        'user_id' => 'integer'
    ];
}