<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DocumentCategory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'slug',
        'tier_id',
        'is_free',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'tier_id' => 'integer',
        'is_free' => 'boolean',
    ];

    public function documentSubcategories(): HasMany
    {
        return $this->hasMany(DocumentSubcategory::class, 'category_id');
    }

    public function documents(): HasMany
{
    return $this->hasMany(Document::class, 'category_id');
}
    public function tier(): BelongsTo
    {
        return $this->belongsTo(TemplateTier::class);
    }
}
