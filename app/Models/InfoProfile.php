<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InfoProfile extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'table',
        'user_id',
        'entity_type',
        'email',
        'phone',
        'company_name',
        'address',
        'tax_identification_number',
        'registration_number',
        'registration_county',
        'date_of_birth',
        'place_of_birth',
        'county_of_birth',
        'pronoun',
        'first_name',
        'last_name',
        'id_document_type',
        'id_document_series',
        'id_document_number',
        'id_document_issuer',
        'id_document_issue_date',
        'personal_id_number',
        'bank_name',
        'bank_city',
        'iban',
        'is_temp',
        'address_street',
        'address_number',
        'postal_code',
        'address_block',
        'address_entrance',
        'address_floor',
        'address_apt',
        'address_city',
        'address_county',
        'address_country',
        'registration_number_jud',
        'registration_number_nr',
        'registration_number_an',
        'company_role',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'date_of_birth' => 'date',
        'id_document_issue_date' => 'date',
        'is_temp' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
