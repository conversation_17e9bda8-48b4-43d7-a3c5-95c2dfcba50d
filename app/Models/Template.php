<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Template extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'category_id',
        'subcategory_id',
        'tier_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'category_id' => 'integer',
        'subcategory_id' => 'integer',
        'tier_id' => 'integer',
    ];



    public function category(): BelongsTo
    {
        return $this->belongsTo(DocumentCategory::class);
    }

    public function subcategory(): BelongsTo
    {
        return $this->belongsTo(DocumentSubcategory::class);
    }

    public function tier(): BelongsTo
    {
        return $this->belongsTo(TemplateTier::class);
    }

    public function documentBricks()
    {
        return $this->belongsToMany(DocumentBrick::class, 'assembly_blocks')
            ->withPivot('order_number')
            ->withTimestamps()
            ->orderBy('assembly_blocks.order_number');
    }

    /**
     * Get the assembly blocks for the template.
     */
    public function assemblyBlocks()
    {
        return $this->hasMany(AssemblyBlock::class);
    }

}
