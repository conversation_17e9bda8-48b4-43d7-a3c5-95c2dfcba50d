<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ClearAllCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:flush-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clears view, cache, config, and route caches';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting cache clearance process...');

        // Clear View Cache
        $this->call('view:clear');
        $this->info('View cache cleared.');
    
        // Clear Application Cache
        $this->call('cache:clear');
        $this->info('Application cache cleared.');
    
        // Clear Configuration Cache
        $this->call('config:clear');
        $this->info('Configuration cache cleared.');
    
        // Clear Route Cache
        $this->call('route:clear');
        $this->info('Route cache cleared.');
    
        $this->info('All caches have been successfully cleared.');
    
        return 0; // Indicates that the command ran successfully
    }
}
