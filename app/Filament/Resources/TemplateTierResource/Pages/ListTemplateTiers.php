<?php

namespace App\Filament\Resources\TemplateTierResource\Pages;

use App\Filament\Resources\TemplateTierResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Notifications\Notification;

class ListTemplateTiers extends ListRecords
{
    protected static string $resource = TemplateTierResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('notifyAll')
                ->label('Notify All Users')
                ->action(function () {

                    $recipient = auth()->user();
 
                    Notification::make()
                        ->title('Saved successfully')
                        ->broadcast($recipient);
                   // Notification::send('All users have been notified.');
                }),

        ];
    }
}
