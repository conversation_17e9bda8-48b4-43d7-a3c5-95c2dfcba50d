<?php

namespace App\Filament\Resources\DocumentBrickResource\Pages;

use App\Filament\Resources\DocumentBrickResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDocumentBricks extends ListRecords
{
    protected static string $resource = DocumentBrickResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
