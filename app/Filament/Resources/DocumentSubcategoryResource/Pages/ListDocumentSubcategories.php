<?php

namespace App\Filament\Resources\DocumentSubcategoryResource\Pages;

use App\Filament\Resources\DocumentSubcategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDocumentSubcategories extends ListRecords
{
    protected static string $resource = DocumentSubcategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
