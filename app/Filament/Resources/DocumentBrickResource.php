<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DocumentBrickResource\Pages;
use App\Filament\Resources\DocumentBrickResource\RelationManagers;
use App\Models\DocumentBrick;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Dotswan\FilamentCodeEditor\Fields\CodeEditor;

class DocumentBrickResource extends Resource
{
    protected static ?string $model = DocumentBrick::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(191),
                CodeEditor::make('content')
                ->required()
               // ->language('html')
                ->columnSpanFull()
         //       ->showLineNumbers()
             //   ->showGutter()
            //    ->showPrintMargin()
             //   ->showHighlightActiveLine()
             //   ->showHighlightSelectedWord()
            //    ->showIndentGuides()
            //    ->showFoldGutter()
            //    ->showSearchReplace()
            //    ->showAutoComplete()
            //    ->showMatchBrackets()
            //    ->showAutoCloseTags()
              
             //  ->wrapLines()

                  ->showCopyButton(true)
             //     ->lineWrapping() // Enables line wrapping
                  ->darkModeTheme('gruvbox-dark')
,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('table')
                    ->searchable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDocumentBricks::route('/'),
            'create' => Pages\CreateDocumentBrick::route('/create'),
            'edit' => Pages\EditDocumentBrick::route('/{record}/edit'),
        ];
    }
}
