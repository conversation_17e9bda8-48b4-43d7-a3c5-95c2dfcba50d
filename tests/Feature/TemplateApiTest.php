<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;

beforeEach(function () {
    // Set a test API key in the config
    Config::set('services.template_api.key', 'test-api-key');
});

test('template creation endpoint works with valid API key', function () {
    // Arrange: Prepare test data
    $data = [
        'name' => 'Test Template',
        'description' => 'Test Description',
        'content' => 'Test Content',
        'category_id' => 1, // Ensure this exists in your test database
        'subcategory_id' => 1, // Ensure this exists in your test database
        'tier_id' => 1, // Ensure this exists in your test database
    ];

    // Act: Make the request with the API key in the header
    $response = $this->withHeaders([
        'X-API-KEY' => 'test-api-key',
    ])->postJson('/api/templates', $data);

    // Assert: Check response
    $response->assertStatus(201)
        ->assertJson([
            'success' => true,
            'message' => 'Template created successfully'
        ]);
});

test('template creation fails with invalid API key', function () {
    // Arrange: Prepare test data
    $data = [
        'name' => 'Test Template',
        'description' => 'Test Description',
        'content' => 'Test Content',
        'category_id' => 1,
        'subcategory_id' => 1,
        'tier_id' => 1,
    ];

    // Act: Make the request with an invalid API key
    $response = $this->withHeaders([
        'X-API-KEY' => 'invalid-key',
    ])->postJson('/api/templates', $data);

    // Assert: Check response
    $response->assertStatus(401)
        ->assertJson([
            'success' => false,
            'message' => 'Invalid API key'
        ]);
});