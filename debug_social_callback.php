<?php

// Simple debugging script to test social callback manually
// This will help us see what's happening in the callback process

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Devdojo\Auth\Http\Controllers\SocialController;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Http\Request;

echo "=== Social Callback Debug Tool ===\n\n";

// Simulate what happens in the callback method
echo "Testing callback method components...\n\n";

try {
    $socialController = new SocialController();
    $reflection = new ReflectionClass($socialController);
    
    // Test the dynamicallySetSocialProviderCredentials method
    echo "1. Testing credential setup for Facebook...\n";
    $credentialsMethod = $reflection->getMethod('dynamicallySetSocialProviderCredentials');
    $credentialsMethod->setAccessible(true);
    $credentialsMethod->invoke($socialController, 'facebook');
    
    echo "   Facebook config after setup:\n";
    echo "   - Client ID: " . config('services.facebook.client_id') . "\n";
    echo "   - Client Secret: " . (config('services.facebook.client_secret') ? 'Set (' . strlen(config('services.facebook.client_secret')) . ' chars)' : 'Missing') . "\n";
    echo "   - Redirect: " . config('services.facebook.redirect') . "\n\n";
    
    // Test Socialite driver creation
    echo "2. Testing Socialite driver creation...\n";
    $driver = Socialite::driver('facebook');
    echo "   ✓ Facebook driver created successfully\n\n";
    
    // Test the getProviderCredentialsWithOverrides method
    echo "3. Testing provider credentials method...\n";
    $providerMethod = $reflection->getMethod('getProviderCredentialsWithOverrides');
    $providerMethod->setAccessible(true);
    $provider = $providerMethod->invoke($socialController, 'facebook');
    
    echo "   Provider object:\n";
    echo "   - Name: " . $provider->name . "\n";
    echo "   - Slug: " . $provider->slug . "\n";
    echo "   - Client ID: " . $provider->client_id . "\n";
    echo "   - Client Secret: " . (strlen($provider->client_secret) > 0 ? 'Set (' . strlen($provider->client_secret) . ' chars)' : 'Missing') . "\n";
    echo "   - Active: " . ($provider->active ? 'Yes' : 'No') . "\n";
    echo "   - Socialite: " . ($provider->socialite ? 'Yes' : 'No') . "\n\n";
    
    // Test user model
    echo "4. Testing user model...\n";
    $userModel = config('auth.providers.users.model');
    echo "   User model: " . $userModel . "\n";
    
    $testUser = app($userModel)->first();
    if ($testUser) {
        echo "   Sample user found: " . $testUser->email . "\n";
        echo "   User has socialProviders relation: " . (method_exists($testUser, 'socialProviders') ? 'Yes' : 'No') . "\n";
    }
    
    echo "\n✓ All callback components test passed\n";
    
} catch (Exception $e) {
    echo "✗ Error during testing: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug completed ===\n";
echo "If all tests passed, try social login again.\n";
echo "The issue might be in the actual OAuth token exchange or user creation process.\n";
