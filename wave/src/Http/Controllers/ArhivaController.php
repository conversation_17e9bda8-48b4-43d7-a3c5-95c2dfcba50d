<?php

namespace Wave\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\DocumentCategory;
use App\Models\Document;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;

class ArhivaController extends Controller
{
    public function show($slug)
    {
        // Get the category from the slug
        $category = DocumentCategory::where('slug', $slug)->firstOrFail();

        // Get documents for this user and category
        $documents = Document::where('user_id', Auth::id())
            ->where('category_id', $category->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Try different view paths to find the correct one
        if (View::exists('themes.anchor.pages.arhiva.category')) {
            return view('themes.anchor.pages.arhiva.category', [
                'category' => $category,
                'documents' => $documents,
            ]);
        } elseif (View::exists('themes.anchor.views.arhiva.category')) {
            return view('themes.anchor.views.arhiva.category', [
                'category' => $category,
                'documents' => $documents,
            ]);
        } else {
            // Fallback to the theme namespace
            return view('theme::arhiva.category', [
                'category' => $category,
                'documents' => $documents,
            ]);
        }
    }
}
