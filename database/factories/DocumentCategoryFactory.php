<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\DocumentCategory;
use App\Models\TemplateTier;

class DocumentCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DocumentCategory::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'table' => $this->faker->word(),
            'name' => $this->faker->name(),
            'slug' => $this->faker->slug(),
            'tier_id' => TemplateTier::factory(),
            'is_free' => $this->faker->boolean(),
        ];
    }
}
