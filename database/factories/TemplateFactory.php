<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\DocumentCategory;
use App\Models\DocumentSubcategory;
use App\Models\Template;
use App\Models\TemplateTier;

class TemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Template::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'table' => $this->faker->word(),
            'name' => $this->faker->name(),
            'description' => $this->faker->text(),
            'category_id' => DocumentCategory::factory(),
            'subcategory_id' => DocumentSubcategory::factory(),
            'tier_id' => TemplateTier::factory(),
        ];
    }
}
