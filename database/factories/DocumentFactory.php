<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Design;
use App\Models\Document;
use App\Models\DocumentCategory;
use App\Models\DocumentSubcategory;
use App\Models\Template;
use App\Models\User;

class DocumentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Document::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'table' => $this->faker->word(),
            'user_id' => User::factory(),
            'template_id' => Template::factory(),
            'subcategory_id' => DocumentSubcategory::factory(),
            'category_id' => DocumentCategory::factory(),
            'document_number' => $this->faker->word(),
            'status' => $this->faker->word(),
            'transaction_id' => $this->faker->numberBetween(-10000, 10000),
            'client_info_profile_id' => $this->faker->numberBetween(-10000, 10000),
            'partner_info_profile_id' => $this->faker->numberBetween(-10000, 10000),
            'third_party_info_profile_id' => $this->faker->numberBetween(-10000, 10000),
            'design_id' => Design::factory(),
            'thumbnail' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'uuid' => $this->faker->uuid(),
            'document_url' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
        ];
    }
}
