<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\AssemblyBlock;
use App\Models\DocumentBrick;
use App\Models\Template;

class AssemblyBlockFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AssemblyBlock::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'table' => $this->faker->word(),
            'template_id' => Template::factory(),
            'document_brick_id' => DocumentBrick::factory(),
            'order_number' => $this->faker->numberBetween(-10000, 10000),
        ];
    }
}
