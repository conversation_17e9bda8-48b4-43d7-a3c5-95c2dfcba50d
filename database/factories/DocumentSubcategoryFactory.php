<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\DocumentCategory;
use App\Models\DocumentSubcategory;
use App\Models\TemplateTier;

class DocumentSubcategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DocumentSubcategory::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'table' => $this->faker->word(),
            'category_id' => DocumentCategory::factory(),
            'name' => $this->faker->name(),
            'is_free' => $this->faker->boolean(),
            'tier_id' => TemplateTier::factory(),
        ];
    }
}
