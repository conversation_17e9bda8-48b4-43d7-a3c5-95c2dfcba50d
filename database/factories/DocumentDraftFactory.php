<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\DocumentDraft;
use App\Models\InfoProfile;
use App\Models\Template;
use App\Models\User;

class DocumentDraftFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DocumentDraft::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'template_id' => Template::factory(),
            'part_a' => InfoProfile::factory()->create()->part_a,
            'part_b' => InfoProfile::factory()->create()->part_b,
            'part_c' => InfoProfile::factory()->create()->part_c,
            'variables' => '{}',
            'content' => $this->faker->paragraphs(3, true),
            'final_content' => $this->faker->text(),
        ];
    }
}
