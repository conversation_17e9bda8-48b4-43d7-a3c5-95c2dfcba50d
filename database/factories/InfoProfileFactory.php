<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\InfoProfile;
use App\Models\User;

class InfoProfileFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = InfoProfile::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'table' => $this->faker->word(),
            'user_id' => User::factory(),
            'entity_type' => $this->faker->randomElement(["individual","srl","sa","ifam","asoc","ong"]),
            'email' => $this->faker->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'company_name' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'address' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'tax_identification_number' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'registration_number' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'registration_county' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'date_of_birth' => $this->faker->date(),
            'place_of_birth' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'county_of_birth' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'pronoun' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'id_document_type' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'id_document_series' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'id_document_number' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'id_document_issuer' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'id_document_issue_date' => $this->faker->date(),
            'personal_id_number' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'bank_name' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'bank_city' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'iban' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'is_temp' => $this->faker->boolean(),
            'address_street' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'address_number' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'address_block' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'address_entrance' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'address_floor' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'address_apt' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'address_city' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'address_county' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'address_country' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'registration_number_jud' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'registration_number_nr' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
            'registration_number_an' => $this->faker->regexify('[A-Za-z0-9]{nullable}'),
        ];
    }
}
