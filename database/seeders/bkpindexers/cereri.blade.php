<?php
    
    use App\Models\DocumentCategory;
    use function Laravel\Folio\{middleware, name};
	middleware('auth');
    name('cereri');
    
    $category = DocumentCategory::where('slug', 'cereri')->firstOrFail();
    $documents = $category->documents()->where('user_id', auth()->id())->get();

?>

<x-layouts.app>
	<x-app.container x-data class="lg:space-y-6" x-cloak>
        
        <x-app.heading
                title="{{ $category->name }}"
                description="Bla."
                :border="false"
            />

    </x-app.container>
</x-layouts.app>