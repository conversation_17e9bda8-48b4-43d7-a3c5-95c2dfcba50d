<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateEntityTypeInInfoProfiles extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {   
        Schema::table('info_profiles', function (Blueprint $table) {
            // Drop the existing entity_type column
            $table->dropColumn('entity_type');
        });

        Schema::table('info_profiles', function (Blueprint $table) {
            // Add the new entity_type column with only 'individual' and 'company' options
            $table->enum('entity_type', ['individual', 'company'])->after('user_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {   
        Schema::table('info_profiles', function (Blueprint $table) {
            // Drop the modified entity_type column
            $table->dropColumn('entity_type');
        });

        Schema::table('info_profiles', function (Blueprint $table) {
            // Re-add the original entity_type column with all options
            $table->enum('entity_type', ['individual', 'srl', 'sa', 'ifam', 'asoc', 'ong'])->after('user_id');
        });
    }
}