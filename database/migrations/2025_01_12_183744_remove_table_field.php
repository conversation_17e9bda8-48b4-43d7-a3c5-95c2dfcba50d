<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $column = 'table';
        $dbName = config('database.connections.mysql.database');
        $allTables = DB::select("SHOW TABLES");
        $keyName = 'Tables_in_'.$dbName;

        foreach ($allTables as $tableObj) {
            $tableName = $tableObj->$keyName;
            if (Schema::hasColumn($tableName, $column)) {
                Schema::table($tableName, function (Blueprint $table) use ($column) {
                    $table->dropColumn($column);
                });
            }
        }
    }

    public function down(): void
    {

    }
};