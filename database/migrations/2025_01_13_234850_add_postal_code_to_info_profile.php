<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('info_profiles', function (Blueprint $table) {
            $table->string('postal_code')->nullable()->after('address_number');
        });
    }

    public function down()
    {
        Schema::table('info_profiles', function (Blueprint $table) {
            $table->dropColumn('postal_code');
        });
    }
};
