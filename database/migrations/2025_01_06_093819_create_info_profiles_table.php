<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('info_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained();
            $table->enum('entity_type', ["individual","srl","sa","ifam","asoc","ong"])->default('individual');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('company_name')->nullable();
            $table->string('address')->nullable();
            $table->string('tax_identification_number')->nullable();
            $table->string('registration_number')->nullable();
            $table->string('registration_county')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->string('place_of_birth')->nullable();
            $table->string('county_of_birth')->nullable();
            $table->string('pronoun')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('id_document_type')->nullable();
            $table->string('id_document_series')->nullable();
            $table->string('id_document_number')->nullable();
            $table->string('id_document_issuer')->nullable();
            $table->date('id_document_issue_date')->nullable();
            $table->string('personal_id_number')->nullable();
            $table->string('bank_name')->nullable();
            $table->string('bank_city')->nullable();
            $table->string('iban')->nullable();
            $table->boolean('is_temp')->default(true);
            $table->string('address_street')->nullable();
            $table->string('address_number')->nullable();
            $table->string('address_block')->nullable();
            $table->string('address_entrance')->nullable();
            $table->string('address_floor')->nullable();
            $table->string('address_apt')->nullable();
            $table->string('address_city')->nullable();
            $table->string('address_county')->nullable();
            $table->string('address_country')->nullable();
            $table->string('registration_number_jud')->nullable();
            $table->string('registration_number_nr')->nullable();
            $table->string('registration_number_an')->nullable();
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('info_profiles');
    }
};
