<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained();
            $table->foreignId('template_id')->constrained();
            $table->foreignId('subcategory_id')->constrained('document_subcategories');
            $table->foreignId('category_id')->constrained('document_categories');
            $table->string('document_number');
            $table->string('status');
            $table->integer('transaction_id')->nullable();
            $table->integer('client_info_profile_id')->nullable();
            $table->integer('partner_info_profile_id')->nullable();
            $table->integer('third_party_info_profile_id')->nullable();
            $table->foreignId('design_id')->constrained()->default('1');
            $table->string('thumbnail')->nullable();
            $table->string('uuid');
            $table->string('document_url')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
