<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('document_drafts', function (Blueprint $table) {
            $table->integer('current_step')->default(1)->after('template_id');
        });
    }

    public function down()
    {
        Schema::table('document_drafts', function (Blueprint $table) {
            $table->dropColumn('current_step');
        });
    }
};