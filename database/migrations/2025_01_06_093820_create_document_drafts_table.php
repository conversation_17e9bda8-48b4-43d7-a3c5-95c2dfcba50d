<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('document_drafts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained();
            $table->foreignId('template_id')->constrained();
            $table->foreignId('part_a')->constrained('info_profiles');
            $table->foreignId('part_b')->constrained('info_profiles')->nullable();
            $table->foreignId('part_c')->constrained('info_profiles')->nullable();
            $table->json('variables')->nullable();
            $table->text('content')->nullable();
            $table->text('final_content')->nullable();
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_drafts');
    }
};
