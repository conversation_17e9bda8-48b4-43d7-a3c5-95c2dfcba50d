<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('social_provider_user', function (Blueprint $table) {
            // Change token column to TEXT to accommodate longer OAuth tokens
            $table->text('token')->change();
            // Also update refresh_token to TEXT for consistency
            $table->text('refresh_token')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('social_provider_user', function (Blueprint $table) {
            // Revert back to varchar(191) - note: this may truncate data
            $table->string('token', 191)->change();
            $table->string('refresh_token', 191)->nullable()->change();
        });
    }
};
