<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('info_profiles', function (Blueprint $table) {
            $table->string('company_role')->nullable();
        });
    }

    public function down()
    {
        Schema::table('info_profiles', function (Blueprint $table) {
            $table->dropColumn('company_role');
        });
    }
};
