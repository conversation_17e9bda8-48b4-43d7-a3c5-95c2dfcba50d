<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('pdfables', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('template_title');
            $table->longText('content');
            $table->string('party_a_rep_name')->nullable();
            $table->string('party_a_rep_role')->nullable();
            $table->string('party_b_rep_name')->nullable();
            $table->string('party_b_rep_role')->nullable();
            $table->integer('document_category');
            $table->foreignId('user_id')->constrained();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('pdfables');
    }
};