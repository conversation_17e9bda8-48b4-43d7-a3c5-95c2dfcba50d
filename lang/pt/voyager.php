<?php

return [
    'date' => [
        'last_week' => 'Semana Passada',
        'last_year' => 'Ano Passado',
        'this_week' => 'Esta Semana',
        'this_year' => 'Este Ano',
    ],

    'generic' => [
        'action'                 => 'Ação',
        'actions'                => 'Ações',
        'add'                    => 'Adicionar',
        'add_folder'             => 'Adicionar Pasta',
        'add_new'                => 'Adicionar',
        'all_done'               => 'Concluído',
        'are_you_sure'           => 'Tem certeza',
        'are_you_sure_delete'    => 'Tem certeza de que deseja remover',
        'auto_increment'         => 'Incremento automático',
        'browse'                 => 'Navegar',
        'builder'                => 'Construtor',
        'cancel'                 => 'Cancelar',
        'choose_type'            => 'Escolha o tipo',
        'click_here'             => 'Clique aqui',
        'close'                  => 'Fechar',
        'compass'                => 'Bússola',
        'created_at'             => 'Criado em',
        'custom'                 => 'Personalizado',
        'dashboard'              => 'Painel de Controle',
        'database'               => 'Base de dados',
        'default'                => 'Padrão',
        'delete'                 => 'Remover',
        'delete_confirm'         => 'Sim, Remover!',
        'delete_question'        => 'Tem certeza de que deseja remover isto',
        'delete_this_confirm'    => 'Sim, exclua isto',
        'deselect_all'           => 'Desmarcar todos',
        'download'               => 'Descarregar',
        'edit'                   => 'Editar',
        'email'                  => 'E-mail',
        'error_deleting'         => 'Oops, ocorreu um problema ao remover',
        'exception'              => 'Exceção',
        'featured'               => 'Destacado',
        'field_does_not_exist'   => 'O campo não existe',
        'how_to_use'             => 'Como usar',
        'index'                  => 'Índice',
        'internal_error'         => 'Erro interno',
        'items'                  => 'item(s)',
        'keep_sidebar_open'      => 'Arrrgh! Soltem as âncoras! (e mantenha a barra lateral aberta)',
        'key'                    => 'Chave',
        'last_modified'          => 'Última modificação',
        'length'                 => 'comprimento',
        'login'                  => 'Login',
        'media'                  => 'Media',
        'menu_builder'           => 'Construtor de Menu',
        'move'                   => 'Mover',
        'name'                   => 'Nome',
        'new'                    => 'Novo',
        'no'                     => 'Não',
        'no_thanks'              => 'Não Obrigado',
        'not_null'               => 'Não Nulo',
        'options'                => 'Opções',
        'password'               => 'Password',
        'permissions'            => 'Permissões',
        'profile'                => 'Perfil',
        'public_url'             => 'URL público',
        'read'                   => 'Ler',
        'rename'                 => 'Renomear',
        'required'               => 'Requerido',
        'return_to_list'         => 'Voltar à lista',
        'route'                  => 'Rota',
        'save'                   => 'Guardar',
        'search'                 => 'Procurar',
        'select_all'             => 'Selecione Todos',
        'settings'               => 'Configurações',
        'showing_entries'        => 'Mostrando :from a :to de :all entrada|Mostrando :from a :to de :all entradas',
        'submit'                 => 'Submeter',
        'successfully_added_new' => 'Adicionado com sucesso',
        'successfully_deleted'   => 'Removido com sucesso',
        'successfully_updated'   => 'Atualizado com sucesso',
        'timestamp'              => 'Timestamp', //todo find suitable translation
        'title'                  => 'Título',
        'type'                   => 'Tipo',
        'unsigned'               => 'Não assinado',
        'unstick_sidebar'        => 'Descolar a barra lateral',
        'update'                 => 'Atualizar',
        'update_failed'          => 'atualização falhou',
        'upload'                 => 'Upload',
        'url'                    => 'URL',
        'view'                   => 'Ver',
        'viewing'                => 'Visualizando',
        'yes'                    => 'Sim',
        'yes_please'             => 'Sim, por favor',
    ],

    'login' => [
        'loggingin'    => 'A iniciar sessão',
        'signin_below' => 'Iniciar sessão abaixo:',
        'welcome'      => 'Bem-vindo ao Voyager. O painel de administração que faltava ao Laravel',
    ],

    'profile' => [
        'avatar'        => 'Avatar',
        'edit'          => 'Editar o meu perfil',
        'edit_user'     => 'Editar Utilizador',
        'password'      => 'Password',
        'password_hint' => 'Deixar vazio para manter o valor atual',
        'role'          => 'Função',
        'user_role'     => 'Função do Utilizador',
    ],

    'settings' => [
        'usage_help'           => 'Pode obter o valor de cada configuração em qualquer lugar em seu site, executando',
        'save'                 => 'Guardar configurações',
        'new'                  => 'Nova configuração',
        'help_name'            => 'Nome da configuração ex: Título do Administrador',
        'help_key'             => 'Chave da configuração ex: title_administrador',
        'help_option'          => '(Opcional, aplica-se apenas a certos tipos, como dropdown ou botão de rádio)',
        'add_new'              => 'Adicionar configuração',
        'delete_question'      => 'Tem certeza de que deseja remover a Configuração :setting?',
        'delete_confirm'       => 'Sim, remover esta configuração',
        'successfully_created' => 'Configurações criadas com sucesso',
        'successfully_saved'   => 'Configurações guardadas com sucesso',
        'successfully_deleted' => 'Configuração removida com sucesso',
        'already_at_top'       => 'Já chegou ao topo da lista',
        'already_at_bottom'    => 'Já chegou ao fundo da lista',
        'moved_order_up'       => 'Configuração :name movida para cima',
        'moved_order_down'     => 'Configuração :name movida para baixo',
        'successfully_removed' => 'Valor :name removido com sucesso',
    ],

    'media' => [
        'add_new_folder'         => 'Adicionar Pasta',
        'audio_support'          => 'O seu navegador não suporta o elemento de áudio.',
        'create_new_folder'      => 'Criar Pasta',
        'delete_folder_question' => 'Ao remover uma pasta irá também remover todos os ficheiros e pastas contidos nela',
        'destination_folder'     => 'Destino da Pasta',
        'drag_drop_info'         => 'Arraste e solte ficheiros ou clique abaixo para carregar',
        'error_already_exists'   => 'Oops, já existe um ficheiro / pasta com esse nome nessa pasta.',
        'error_creating_dir'     => 'Oops, ocorreu algo inesperado a criar a pasta, por favor verifique as suas permissões',
        'error_deleting_file'    => 'Oops, ocorreu algo inesperado removendo este ficheiro, por favor verifique as suas permissões',
        'error_deleting_folder'  => 'Oops, ocorreu algo inesperado removendo esta pasta, por favor verifique as suas permissões',
        'error_may_exist'        => 'Talvez um Ficheiro ou Pasta exista com esse nome. Por favor tente com outro nome, ou apague o ficheiro correspondente.',
        'error_moving'           => 'Oops, ocorreu um problema ao mover esse ficheiro / pasta, verifique as suas permissões.',
        'error_uploading'        => 'Falha ao Copiar: Ocorreu um erro desconhecido!',
        'folder_exists_already'  => 'Oops, essa pasta já existe, por favor remova essa pasta se desejar criar uma nova',
        'image_does_not_exist'   => 'A imagem não existe',
        'image_removed'          => 'Imagem removida',
        'library'                => 'Biblioteca de Media',
        'loading'                => 'A CARREGAR OS SEUS FICHEIROS DE MÍDIA',
        'move_file_folder'       => 'Mover Ficheiro/pasta',
        'new_file_folder'        => 'Novo Nome do Ficheiro/Pasta',
        'new_folder_name'        => 'Novo Nome da Pasta',
        'no_files_here'          => 'Não há ficheiros aqui.',
        'no_files_in_folder'     => 'Nenhum ficheiro nesta pasta.',
        'nothing_selected'       => 'Nenhum ficheiro ou pasta selecionada',
        'rename_file_folder'     => 'Renomear Ficheiro/Pasta',
        'success_uploaded_file'  => 'Ficheiro carregado com sucesso!',
        'success_uploading'      => 'Imagem carregada com sucesso!',
        'uploading_wrong_type'   => 'Falha de envio: Formato do ficheiro não suportado ou é muito grande para ser carregado!',
        'video_support'          => 'O seu navegador não suporta a tag de vídeo.',
    ],

    'menu_builder' => [
        'color'                => 'Cor em RGB ou hex (opcional)',
        'color_ph'             => 'Cor (ex. #ffffff ou rgb(255, 255, 255)',
        'create_new_item'      => 'Criar um novo item de menu',
        'delete_item_confirm'  => 'Sim, Remover este item de menu',
        'delete_item_question' => 'Tem certeza de que deseja remover este item de menu?',
        'drag_drop_info'       => 'Arraste e solte os itens do menu para os reorganizar.',
        'dynamic_route'        => 'Rota Dinâmica',
        'edit_item'            => 'Editar item de menu',
        'icon_class'           => 'Classe do Ícone da Fonte para o item de menu (Use ',
        'icon_class2'          => 'Classe da Fonte Voyager</a>)',
        'icon_class_ph'        => 'Classe do Ícone (opcional)',
        'item_route'           => 'Rota do item de menu',
        'item_title'           => 'Título do item de menu',
        'link_type'            => 'Tipo de link',
        'new_menu_item'        => 'Novo Item de Menu',
        'open_in'              => 'Abrir em',
        'open_new'             => 'Nova Guia/Janela',
        'open_same'            => 'Mesma Guia/Janela',
        'route_parameter'      => 'Parâmetros de Rotas (se aplicado)',
        'static_url'           => 'URL Estático',
        'successfully_created' => 'Novo item de menu criado com sucesso.',
        'successfully_deleted' => 'Item de menu removido com sucesso',
        'successfully_updated' => 'Item de menu atualizado com sucesso.',
        'updated_order'        => 'Ordem de menu atualizada com sucesso.',
        'url'                  => 'URL do item de menu',
        'usage_hint'           => 'Pode apresentar um menu em qualquer lugar no seu site, executando| Pode apresentar este menu em qualquer lugar no seu site, executando',
    ],

    'post' => [
        'category'         => 'Categoria da Publicação',
        'content'          => 'Conteúdo da Publicação',
        'details'          => 'Detalhes da Publicação',
        'excerpt'          => 'Excerto <small>Pequena descrição desta publicação</small>',
        'image'            => 'Publicar imagem',
        'meta_description' => 'Descrição de Meta',
        'meta_keywords'    => 'palavras-chave de Meta',
        'new'              => 'Criar nova publicação',
        'seo_content'      => 'Conteúdo do SEO',
        'seo_title'        => 'Título SEO',
        'slug'             => 'URL slug',
        'status'           => 'Status da Publicação',
        'status_draft'     => 'rascunho',
        'status_pending'   => 'pendente',
        'status_published' => 'Publicados',
        'title'            => 'Título do cargo',
        'title_sub'        => 'O título da sua Publicação',
        'update'           => 'Alterar Publicação',
    ],

    'database' => [
        'add_bread'                 => 'Adicionar BREAD a esta tabela',
        'add_new_column'            => 'Adicionar Novo Campo',
        'add_softdeletes'           => 'Adicionar Soft Deletes',
        'add_timestamps'            => 'Adicionar Timestamps',
        'already_exists'            => 'já existe',
        'already_exists_table'      => 'A Tabela :table já existe',
        'bread_crud_actions'        => 'Ações BREAD/CRUD',
        'bread_info'                => 'Informação do BREAD',
        'column'                    => 'Campo',
        'composite_warning'         => 'Atenção: este campo faz parte dos índices compostos',
        'controller_name'           => 'Nome do Controller',
        'controller_name_hint'      => 'ex. PageController, se não preencher irá usar o BREAD Controller',
        'create_bread_for_table'    => 'Criar BREAD para a tabela :table',
        'create_migration'          => 'Criar Migration para esta tabela?',
        'create_model_table'        => 'Criar Model para esta tabela?',
        'create_new_table'          => 'Criar Tabela',
        'create_your_new_table'     => 'Criar a Nova Tabela',
        'default'                   => 'Pré-definido',
        'delete_bread'              => 'Remover BREAD',
        'delete_bread_before_table' => 'Por favor, remova o BREAD desta tabela antes de remover a tabela.',
        'delete_table_bread_conf'   => 'Sim, remover este BREAD',
        'delete_table_bread_quest'  => 'Tem a certeza que deseja remover o BREAD para a tabela :table?',
        'delete_table_confirm'      => 'Sim, remover esta tabela',
        'delete_table_question'     => 'Tem a certeza que deseja remover a tabela :table?',
        'description'               => 'Descrição',
        'display_name'              => 'Nome a Apresentar',
        'display_name_plural'       => 'Nome a Apresentar (Plural)',
        'display_name_singular'     => 'Nome a Apresentar (Singular)',
        'edit_bread'                => 'Alterar BREAD',
        'edit_bread_for_table'      => 'Alterar BREAD da tabela :table',
        'edit_rows'                 => 'Alterar as linhas para a tabela :table abaixo',
        'edit_table'                => 'Alterar a tabela :table abaixo',
        'edit_table_not_exist'      => 'A tabela que pretende remover não existe',
        'error_creating_bread'      => 'Oops, ocorreu algo inesperado ao criar este BREAD',
        'error_removing_bread'      => 'Oops, ocorreu algo inesperado ao Remover este BREAD',
        'error_updating_bread'      => 'Oops, ocorreu algo inesperado ao alterar este BREAD',
        'extra'                     => 'Extra',
        'field'                     => 'Campo',
        'field_safe_failed'         => 'Erro ao gravar o campo :field, voltando atrás!',
        'generate_permissions'      => 'Gerar Permissões',
        'icon_class'                => 'Icon para usar nesta Tabela',
        'icon_hint'                 => 'Icon (opcional) Usar a',
        'icon_hint2'                => 'Voyager Font Class',
        'index'                     => 'INDEX',
        'input_type'                => 'Tipo de Input',
        'key'                       => 'Key',
        'model_class'               => 'Nome da Classe do Model',
        'model_name'                => 'Nome do Model',
        'model_name_ph'             => 'ex. \App\Models\User, se vazio irá tentar usar o nome da tabela',
        'name_warning'              => 'Por favor adicione o nome da coluna para criar o index',
        'no_composites_warning'     => 'Esta tabela tem composite indexes. Nota, eles não são suportados de momento. Tenha atenção ao tentar adicionar/remover indexes.',
        'null'                      => 'Null',
        'optional_details'          => 'Opções Adicionais',
        'primary'                   => 'PRIMARY',
        'server_pagination'         => 'Paginação no Servidor',
        'success_create_table'      => 'Tabela :table criada com sucesso',
        'success_created_bread'     => 'BREAD criado com sucesso',
        'success_delete_table'      => 'Tabela :table removida com sucesso',
        'success_remove_bread'      => 'BREAD :datatype removido com sucesso',
        'success_update_bread'      => 'BREAD :datatype alterado com sucesso',
        'success_update_table'      => 'Tabela :table alterada com sucesso',
        'table_actions'             => 'Ações da Tabela',
        'table_columns'             => 'Campos da Tabela',
        'table_has_index'           => 'A tabela já tem um primary index.',
        'table_name'                => 'Nome da Tabela',
        'table_no_columns'          => 'A tabela não tem campos...',
        'type'                      => 'Tipo',
        'type_not_supported'        => 'Este tipo de campo não é suportado',
        'unique'                    => 'UNIQUE',
        'unknown_type'              => 'Tipo Desconhecido',
        'update_table'              => 'Alterar Tabela',
        'url_slug'                  => 'URL Slug (único)',
        'url_slug_ph'               => 'URL slug (ex. posts)',
        'visibility'                => 'Visibilidade',
    ],

    'dimmer' => [
        'page'           => 'Página|Páginas',
        'page_link_text' => 'Ver todas as páginas',
        'page_text'      => 'Tem :count :string na sua base de dados. Clique no botão abaixo para ver todas as páginas.',
        'post'           => 'Publicação|Publicações',
        'post_link_text' => 'Ver todas as publicações',
        'post_text'      => 'Tem :count :string na sua base de dados. Clique no botão abaixo para ver todas as publicações.',
        'user'           => 'Utilizador|Utilizadores',
        'user_link_text' => 'Ver todos os utilizadores',
        'user_text'      => 'Tem :count :string na sua base de dados. Clique no botão abaixo para ver todos os utilizadores.',
    ],

    'form' => [
        'field_password_keep'          => 'Deixar vazio para manter o atual',
        'field_select_dd_relationship' => 'Make sure to setup the appropriate relationship in the :method method of the :class class.',
        'type_checkbox'                => 'Check Box',
        'type_codeeditor'              => 'Editor de Código',
        'type_file'                    => 'Ficheiro',
        'type_image'                   => 'Imagem',
        'type_radiobutton'             => 'Radio Button',
        'type_richtextbox'             => 'Rich Textbox',
        'type_selectdropdown'          => 'Select Dropdown',
        'type_textarea'                => 'Text Area',
        'type_textbox'                 => 'Text Box',
    ],

    'datatable' => [
        'sEmptyTable'     => 'Não há registos para apresentar',
        'sInfo'           => 'Mostrando de _START_ até _END_ de _TOTAL_ registos',
        'sInfoEmpty'      => 'Mostrando de 0 até 0 de 0 registos',
        'sInfoFiltered'   => '(filtrado de _MAX_ registos no total)',
        'sInfoPostFix'    => '',
        'sInfoThousands'  => ',',
        'sLengthMenu'     => 'Mostrar _MENU_ registos',
        'sLoadingRecords' => 'A Carregar...',
        'sProcessing'     => 'A processar...',
        'sSearch'         => 'Procurar:',
        'sZeroRecords'    => 'Não foram encontrados resultados',
        'oPaginate'       => [
            'sFirst'    => 'Primeiro',
            'sPrevious' => 'Anterior',
            'sNext'     => 'Seguinte',
            'sLast'     => 'Último',
        ],
        'oAria' => [
            'sSortAscending'  => ': ativar para ordenar de forma crescente',
            'sSortDescending' => ': ativar para ordenar de forma decrescente',
        ],
    ],

    'theme' => [
        'footer_copyright'  => 'Produzido com <i class="voyager-heart"></i> por',
        'footer_copyright2' => 'Produzido com rum e mais rum',
    ],

    'json' => [
        'invalid'           => 'JSON Inválido',
        'invalid_message'   => 'Submeteu um JSON inválido.',
        'valid'             => 'JSON Válido',
        'validation_errors' => 'Erros de validação',
    ],

    'analytics' => [
        'by_pageview'            => 'Por pageview',
        'by_sessions'            => 'Por sessions',
        'by_users'               => 'Por users',
        'no_client_id'           => 'Para aceder ao analytics precisa adicionar nas Configurações do Voyager o key <code>google_analytics_client_id </code> com o código google analytics client id. Obtenha o seu key através do Google developer console:',
        'set_view'               => 'Selecionar Vista',
        'this_vs_last_week'      => 'Esta Semana vs Semana Passada',
        'this_vs_last_year'      => 'Este Ano vs Ano Passado',
        'top_browsers'           => 'Top Browsers',
        'top_countries'          => 'Top Países',
        'various_visualizations' => 'Visualizações várias',
    ],

    'error' => [
        'symlink_created_text'   => 'We just created the missing symlink for you.',
        'symlink_created_title'  => 'Missing storage symlink created',
        'symlink_failed_text'    => 'We failed to generate the missing symlink for your application. It seems like your hosting provider does not support it.',
        'symlink_failed_title'   => 'Could not create missing storage symlink',
        'symlink_missing_button' => 'Fix it',
        'symlink_missing_text'   => 'We could not find a storage symlink. This could cause problems with loading media files from the browser.',
        'symlink_missing_title'  => 'Missing storage symlink',
    ],
];
