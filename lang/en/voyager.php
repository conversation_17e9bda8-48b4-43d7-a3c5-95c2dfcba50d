<?php

return [
    'date' => [
        'last_week' => 'Last Week',
        'last_year' => 'Last Year',
        'this_week' => 'This Week',
        'this_year' => 'This Year',
    ],

    'generic' => [
        'action'                 => 'Action',
        'actions'                => 'Actions',
        'add'                    => 'Add',
        'add_folder'             => 'Add Folder',
        'add_new'                => 'Add New',
        'all_done'               => 'All done',
        'are_you_sure'           => 'Are you sure',
        'are_you_sure_delete'    => 'Are you sure you want to delete',
        'auto_increment'         => 'Auto Increment',
        'browse'                 => 'Browse',
        'builder'                => 'Builder',
        'bulk_delete'            => 'Bulk delete',
        'bulk_delete_confirm'    => 'Yes, Delete These',
        'bulk_delete_nothing'    => 'You haven\'t selected anything to delete',
        'cancel'                 => 'Cancel',
        'choose_type'            => 'Choose Type',
        'click_here'             => 'Click Here',
        'close'                  => 'Close',
        'compass'                => 'Compass',
        'created_at'             => 'Created at',
        'custom'                 => 'Custom',
        'dashboard'              => 'Dashboard',
        'database'               => 'Database',
        'default'                => 'Default',
        'delete'                 => 'Delete',
        'delete_confirm'         => 'Yes, Delete it!',
        'delete_question'        => 'Are you sure you want to delete this',
        'delete_this_confirm'    => 'Yes, Delete This',
        'deselect_all'           => 'Deselect All',
        'download'               => 'Download',
        'edit'                   => 'Edit',
        'email'                  => 'E-mail',
        'error_deleting'         => 'Sorry it appears there was a problem deleting this',
        'exception'              => 'Exception',
        'featured'               => 'Featured',
        'field_does_not_exist'   => 'Field does not exist',
        'how_to_use'             => 'How To Use',
        'index'                  => 'Index',
        'internal_error'         => 'Internal error',
        'items'                  => 'item(s)',
        'keep_sidebar_open'      => 'Yarr! Drop the anchors! (and keep the sidebar open)',
        'key'                    => 'Key',
        'last_modified'          => 'Last modified',
        'length'                 => 'Length',
        'login'                  => 'Login',
        'media'                  => 'Media',
        'menu_builder'           => 'Menu Builder',
        'move'                   => 'Move',
        'name'                   => 'Name',
        'new'                    => 'New',
        'no'                     => 'No',
        'no_thanks'              => 'No Thanks',
        'not_null'               => 'Not Null',
        'options'                => 'Options',
        'password'               => 'Password',
        'permissions'            => 'Permissions',
        'profile'                => 'Profile',
        'public_url'             => 'Public URL',
        'read'                   => 'Read',
        'rename'                 => 'Rename',
        'required'               => 'Required',
        'return_to_list'         => 'Return to List',
        'route'                  => 'Route',
        'save'                   => 'Save',
        'search'                 => 'Search',
        'select_all'             => 'Select All',
        'select_group'           => 'Select Existing Group or Add New',
        'settings'               => 'Settings',
        'showing_entries'        => 'Showing :from to :to of :all entrie|Showing :from to :to of :all entries',
        'submit'                 => 'Submit',
        'successfully_added_new' => 'Successfully Added New',
        'successfully_deleted'   => 'Successfully Deleted',
        'successfully_updated'   => 'Successfully Updated',
        'timestamp'              => 'Timestamp',
        'title'                  => 'Title',
        'type'                   => 'Type',
        'unsigned'               => 'Unsigned',
        'unstick_sidebar'        => 'Unstick the sidebar',
        'update'                 => 'Update',
        'update_failed'          => 'Update Failed',
        'upload'                 => 'Upload',
        'url'                    => 'URL',
        'view'                   => 'View',
        'viewing'                => 'Viewing',
        'yes'                    => 'Yes',
        'yes_please'             => 'Yes, Please',
    ],

    'login' => [
        'loggingin'    => 'Logging in',
        'signin_below' => 'Sign In Below:',
        'welcome'      => 'Welcome to Voyager. The Missing Admin for Laravel',
    ],

    'profile' => [
        'avatar'        => 'Avatar',
        'edit'          => 'Edit My Profile',
        'edit_user'     => 'Edit User',
        'password'      => 'Password',
        'password_hint' => 'Leave empty to keep the same',
        'role'          => 'Role',
        'user_role'     => 'User Role',
    ],

    'settings' => [
        'usage_help'           => 'You can get the value of each setting anywhere on your site by calling',
        'save'                 => 'Save Settings',
        'new'                  => 'New Setting',
        'help_name'            => 'Setting name ex: Admin Title',
        'help_key'             => 'Setting key ex: admin_title',
        'help_option'          => '(optional, only applies to certain types like dropdown box or radio button)',
        'add_new'              => 'Add New Setting',
        'delete_question'      => 'Are you sure you want to delete the :setting Setting?',
        'delete_confirm'       => 'Yes, Delete This Setting',
        'successfully_created' => 'Successfully Created Settings',
        'successfully_saved'   => 'Successfully Saved Settings',
        'successfully_deleted' => 'Successfully Deleted Setting',
        'already_at_top'       => 'This is already at the top of the list',
        'already_at_bottom'    => 'This is already at the bottom of the list',
        'key_already_exists'   => 'The key :key already exists',
        'moved_order_up'       => 'Moved :name setting order up',
        'moved_order_down'     => 'Moved :name setting order down',
        'successfully_removed' => 'Successfully removed :name value',
        'group_general'        => 'General',
        'group_admin'          => 'Admin',
        'group_site'           => 'Site',
        'group'                => 'Group',
        'help_group'           => 'Group this setting is assigned to',
    ],

    'media' => [
        'add_new_folder'         => 'Add New Folder',
        'audio_support'          => 'Your browser does not support the audio element.',
        'create_new_folder'      => 'Create New Folder',
        'delete_folder_question' => 'Deleting a folder will remove all files and folders contained inside',
        'destination_folder'     => 'Destination Folder',
        'drag_drop_info'         => 'Drag and drop files or click below to upload',
        'error_already_exists'   => 'Sorry there is already a file/folder with that existing name in that folder.',
        'error_creating_dir'     => 'Sorry something seems to have gone wrong with creating the directory, '.
                                    'please check your permissions',
        'error_deleting_file' => 'Sorry something seems to have gone wrong deleting this file, please check your '.
                                    'permissions',
        'error_deleting_folder' => 'Sorry something seems to have gone wrong when deleting this folder, '.
                                    'please check your permissions',
        'error_may_exist' => 'File or Folder may already exist with that name. Please choose another name or '.
                                    'delete the other file.',
        'error_moving' => 'Sorry there seems to be a problem moving that file/folder, please make '.
                                    'sure you have the correct permissions.',
        'error_uploading'       => 'Upload Fail: Unknown error occurred!',
        'folder_exists_already' => 'Sorry that folder already exists, please delete that folder if you wish '.
                                    'to re-create it',
        'image_does_not_exist'  => 'Image does not exist',
        'image_removed'         => 'Image removed',
        'library'               => 'Media Library',
        'loading'               => 'LOADING YOUR MEDIA FILES',
        'move_file_folder'      => 'Move File/Folder',
        'new_file_folder'       => 'New File/Folder Name',
        'new_folder_name'       => 'New Folder Name',
        'no_files_here'         => 'No files here.',
        'no_files_in_folder'    => 'No files in this folder.',
        'nothing_selected'      => 'No file or folder selected',
        'rename_file_folder'    => 'Rename File/Folder',
        'success_uploaded_file' => 'Successfully uploaded new file!',
        'success_uploading'     => 'Image successfully uploaded!',
        'uploading_wrong_type'  => 'Upload Fail: Unsupported file format or It is too large to upload!',
        'video_support'         => 'Your browser does not support the video tag.',
        'crop'                  => 'Crop',
        'crop_and_create'       => 'Crop & Create',
        'crop_override_confirm' => 'It will override the original image, are you sure?',
        'crop_image'            => 'Crop Image',
        'success_crop_image'    => 'Successfully crop the image',
        'height'                => 'Height: ',
        'width'                 => 'Width: ',
    ],

    'menu_builder' => [
        'color'                => 'Color in RGB or hex (optional)',
        'color_ph'             => 'Color (ex. #ffffff or rgb(255, 255, 255)',
        'create_new_item'      => 'Create a New Menu Item',
        'delete_item_confirm'  => 'Yes, Delete This Menu Item',
        'delete_item_question' => 'Are you sure you want to delete this menu item?',
        'drag_drop_info'       => 'Drag and drop the menu Items below to re-arrange them.',
        'dynamic_route'        => 'Dynamic Route',
        'edit_item'            => 'Edit Menu Item',
        'icon_class'           => 'Font Icon class for the Menu Item (Use a',
        'icon_class2'          => 'Voyager Font Class</a>)',
        'icon_class_ph'        => 'Icon Class (optional)',
        'item_route'           => 'Route for the menu item',
        'item_title'           => 'Title of the Menu Item',
        'link_type'            => 'Link type',
        'new_menu_item'        => 'New Menu Item',
        'open_in'              => 'Open In',
        'open_new'             => 'New Tab/Window',
        'open_same'            => 'Same Tab/Window',
        'route_parameter'      => 'Route parameters (if any)',
        'static_url'           => 'Static URL',
        'successfully_created' => 'Successfully Created New Menu Item.',
        'successfully_deleted' => 'Successfully Deleted Menu Item.',
        'successfully_updated' => 'Successfully Updated Menu Item.',
        'updated_order'        => 'Successfully updated menu order.',
        'url'                  => 'URL for the Menu Item',
        'usage_hint'           => 'You can output a menu anywhere on your site by calling|You can output '.
                                  'this menu anywhere on your site by calling',
    ],

    'post' => [
        'category'         => 'Post Category',
        'content'          => 'Post Content',
        'details'          => 'Post Details',
        'excerpt'          => 'Excerpt <small>Small description of this post</small>',
        'image'            => 'Post Image',
        'meta_description' => 'Meta Description',
        'meta_keywords'    => 'Meta Keywords',
        'new'              => 'Create New Post',
        'seo_content'      => 'SEO Content',
        'seo_title'        => 'Seo Title',
        'slug'             => 'URL slug',
        'status'           => 'Post Status',
        'status_draft'     => 'draft',
        'status_pending'   => 'pending',
        'status_published' => 'published',
        'title'            => 'Post Title',
        'title_sub'        => 'The title for your post',
        'update'           => 'Update Post',
    ],

    'database' => [
        'add_bread'                 => 'Add BREAD to this table',
        'add_new_column'            => 'Add New Column',
        'add_softdeletes'           => 'Add Soft Deletes',
        'add_timestamps'            => 'Add Timestamps',
        'already_exists'            => 'already exists',
        'already_exists_table'      => 'Table :table already exists',
        'bread_crud_actions'        => 'BREAD/CRUD Actions',
        'bread_info'                => 'BREAD info',
        'browse_bread'              => 'Browse BREAD',
        'column'                    => 'Column',
        'composite_warning'         => 'Warning: this column is part of a composite index',
        'controller_name'           => 'Controller Name',
        'controller_name_hint'      => 'ex. PageController, if left empty will use the BREAD Controller',
        'create_bread_for_table'    => 'Create BREAD for :table table',
        'create_migration'          => 'Create migration for this table?',
        'create_model_table'        => 'Create model for this table?',
        'create_new_table'          => 'Create New Table',
        'create_your_new_table'     => 'Create Your New Table',
        'default'                   => 'Default',
        'delete_bread'              => 'Delete BREAD',
        'delete_bread_before_table' => 'Please make sure to remove the BREAD on this table before deleting the table.',
        'delete_table_bread_conf'   => 'Yes, remove the BREAD',
        'delete_table_bread_quest'  => 'Are you sure you want to delete the BREAD for the :table table?',
        'delete_table_confirm'      => 'Yes, delete this table',
        'delete_table_question'     => 'Are you sure you want to delete the :table table?',
        'description'               => 'Description',
        'display_name'              => 'Display Name',
        'display_name_plural'       => 'Display Name (Plural)',
        'display_name_singular'     => 'Display Name (Singular)',
        'edit_bread'                => 'Edit BREAD',
        'edit_bread_for_table'      => 'Edit BREAD for :table table',
        'edit_rows'                 => 'Edit the rows for the :table table below',
        'edit_table'                => 'Edit the :table table below',
        'edit_table_not_exist'      => 'The table you want to edit doesn\'t exist',
        'error_creating_bread'      => 'Sorry it appears there may have been a problem creating this BREAD',
        'error_removing_bread'      => 'Sorry it appears there was a problem removing this BREAD',
        'error_updating_bread'      => 'Sorry it appears there may have been a problem updating this BREAD',
        'extra'                     => 'Extra',
        'field'                     => 'Field',
        'field_safe_failed'         => 'Failed to save field :field, we\'re rolling back!',
        'generate_permissions'      => 'Generate Permissions',
        'icon_class'                => 'Icon to use for this Table',
        'icon_hint'                 => 'Icon (optional) Use a',
        'icon_hint2'                => 'Voyager Font Class',
        'index'                     => 'INDEX',
        'input_type'                => 'Input Type',
        'key'                       => 'Key',
        'model_class'               => 'Model Class Name',
        'model_name'                => 'Model Name',
        'model_name_ph'             => 'ex. \App\Models\User, if left empty will try and use the table name',
        'name_warning'              => 'Please name the column before adding an index',
        'no_composites_warning'     => 'This table has composite indexes. Please note that they are not supported at the moment. Be careful when trying to add/remove indexes.',
        'null'                      => 'Null',
        'optional_details'          => 'Optional Details',
        'policy_class'              => 'Policy Class Name',
        'policy_name'               => 'Policy Name',
        'policy_name_ph'            => 'ex. \App\Policies\UserPolicy, if left empty will try and use the default',
        'primary'                   => 'PRIMARY',
        'server_pagination'         => 'Server-side Pagination',
        'success_create_table'      => 'Successfully created :table table',
        'success_created_bread'     => 'Successfully created new BREAD',
        'success_delete_table'      => 'Successfully deleted :table table',
        'success_remove_bread'      => 'Successfully removed BREAD from :datatype',
        'success_update_bread'      => 'Successfully updated the :datatype BREAD',
        'success_update_table'      => 'Successfully updated :table table',
        'table_actions'             => 'Table Actions',
        'table_columns'             => 'Table Columns',
        'table_has_index'           => 'The table already has a primary index.',
        'table_name'                => 'Table Name',
        'table_no_columns'          => 'The table has no columns...',
        'type'                      => 'Type',
        'type_not_supported'        => 'This type is not supported',
        'unique'                    => 'UNIQUE',
        'unknown_type'              => 'Unknown Type',
        'update_table'              => 'Update Table',
        'url_slug'                  => 'URL Slug (must be unique)',
        'url_slug_ph'               => 'URL slug (ex. posts)',
        'visibility'                => 'Visibility',
        'relationship'              => [
            'relationship'         => 'Relationship',
            'relationships'        => 'Relationships',
            'has_one'              => 'Has One',
            'has_many'             => 'Has Many',
            'belongs_to'           => 'Belongs To',
            'belongs_to_many'      => 'Belongs To Many',
            'which_column_from'    => 'Which column from the',
            'is_used_to_reference' => 'is used to reference the',
            'pivot_table'          => 'Pivot Table',
            'selection_details'    => 'Selection Details',
            'display_the'          => 'Display the',
            'store_the'            => 'Store the',
            'easy_there'           => 'Easy there Captain',
            'before_create'        => 'Before you can create a new relationship you will need to create the BREAD first.<br> Then, return back to edit the BREAD and you will be able to add relationships.<br> Thanks.',
            'cancel'               => 'Cancel',
            'add_new'              => 'Add New relationship',
            'open'                 => 'Open',
            'close'                => 'Close',
            'relationship_details' => 'Relationship Details',
            'browse'               => 'Browse',
            'read'                 => 'Read',
            'edit'                 => 'Edit',
            'add'                  => 'Add',
            'delete'               => 'Delete',
            'create'               => 'Create a Relationship',
            'namespace'            => 'Model Namespace (ex. App\Category)',
        ],
    ],

    'dimmer' => [
        'page'           => 'Page|Pages',
        'page_link_text' => 'View all pages',
        'page_text'      => 'You have :count :string in your database. Click on button below to view all pages.',
        'post'           => 'Post|Posts',
        'post_link_text' => 'View all posts',
        'post_text'      => 'You have :count :string in your database. Click on button below to view all posts.',
        'user'           => 'User|Users',
        'user_link_text' => 'View all users',
        'user_text'      => 'You have :count :string in your database. Click on button below to view all users.',
    ],

    'form' => [
        'field_password_keep'          => 'Leave empty to keep the same',
        'field_select_dd_relationship' => 'Make sure to setup the appropriate relationship in the :method method of '.
                                          'the :class class.',
        'type_checkbox'       => 'Check Box',
        'type_codeeditor'     => 'Code Editor',
        'type_file'           => 'File',
        'type_image'          => 'Image',
        'type_radiobutton'    => 'Radio Button',
        'type_richtextbox'    => 'Rich Textbox',
        'type_selectdropdown' => 'Select Dropdown',
        'type_textarea'       => 'Text Area',
        'type_textbox'        => 'Text Box',
    ],

    // DataTable translations from: https://github.com/DataTables/Plugins/tree/master/i18n
    'datatable' => [
        'sEmptyTable'     => 'No data available in table',
        'sInfo'           => 'Showing _START_ to _END_ of _TOTAL_ entries',
        'sInfoEmpty'      => 'Showing 0 to 0 of 0 entries',
        'sInfoFiltered'   => '(filtered from _MAX_ total entries)',
        'sInfoPostFix'    => '',
        'sInfoThousands'  => ',',
        'sLengthMenu'     => 'Show _MENU_ entries',
        'sLoadingRecords' => 'Loading...',
        'sProcessing'     => 'Processing...',
        'sSearch'         => 'Search:',
        'sZeroRecords'    => 'No matching records found',
        'oPaginate'       => [
            'sFirst'    => 'First',
            'sLast'     => 'Last',
            'sNext'     => 'Next',
            'sPrevious' => 'Previous',
        ],
        'oAria' => [
            'sSortAscending'  => ': activate to sort column ascending',
            'sSortDescending' => ': activate to sort column descending',
        ],
    ],

    'theme' => [
        'footer_copyright'  => 'Made with <i class="voyager-heart"></i> by',
        'footer_copyright2' => 'Made with rum and even more rum',
    ],

    'json' => [
        'invalid'           => 'Invalid Json',
        'invalid_message'   => 'Seems like you introduced some invalid JSON.',
        'valid'             => 'Valid Json',
        'validation_errors' => 'Validation errors',
    ],

    'analytics' => [
        'by_pageview'  => 'By pageview',
        'by_sessions'  => 'By sessions',
        'by_users'     => 'By users',
        'no_client_id' => 'To view analytics you\'ll need to get a google analytics client id and '.
                                     'add it to your settings for the key <code>google_analytics_client_id'.
                                     '</code>. Get your key in your Google developer console:',
        'set_view'               => 'Select a View',
        'this_vs_last_week'      => 'This Week vs Last Week',
        'this_vs_last_year'      => 'This Year vs Last Year',
        'top_browsers'           => 'Top Browsers',
        'top_countries'          => 'Top Countries',
        'various_visualizations' => 'Various visualizations',
    ],

    'error' => [
        'symlink_created_text'  => 'We just created the missing symlink for you.',
        'symlink_created_title' => 'Missing storage symlink created',
        'symlink_failed_text'   => 'We failed to generate the missing symlink for your application. '.
                                    'It seems like your hosting provider does not support it.',
        'symlink_failed_title'   => 'Could not create missing storage symlink',
        'symlink_missing_button' => 'Fix it',
        'symlink_missing_text'   => 'We could not find a storage symlink. This could cause problems with '.
                                    'loading media files from the browser.',
        'symlink_missing_title' => 'Missing storage symlink',
    ],
];
