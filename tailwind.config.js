import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import preset from './vendor/filament/support/tailwind.config.preset';
import fs from 'fs';
import path from 'path';
import daisyui from 'daisyui';

const themeFilePath = path.resolve(__dirname, 'theme.json');
const activeTheme = fs.existsSync(themeFilePath) ? JSON.parse(fs.readFileSync(themeFilePath, 'utf8')).name : 'anchor';

/** @type {import('tailwindcss').Config} */
export default {
    darkMode: 'class',
    presets: [preset],
    content: [
        './app/Filament/**/*.php',
        './resources/views/livewire/**/*.blade.php',
        './resources/views/filament/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './vendor/awcodes/filament-tiptap-editor/resources/**/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/views/components/**/*.blade.php',
        './resources/views/components/blade.php',
        './wave/resources/views/**/*.blade.php',
        './resources/themes/' + activeTheme + '/**/*.blade.php',
        './resources/plugins/**/*.php',
        './config/*.php'
    ],
    safelist: ['checkbox-xs', 'select-xs', 'input-xs', 'gap-4',
        'select-sm', 'btn-primary', 'btn-secondary', 'bg-primary', 'bg-secondary',
        'text-end', 'btn-success', 'btn-warning', 'btn-info', 'btn-ghost', 'btn-accent', 'btn-error',
        'items-baseline', '-mb-2'],
    theme: {
        extend: {
            animation: {
                'marquee': 'marquee 25s linear infinite',
            },
            keyframes: {
                'marquee': {
                    from: { transform: 'translateX(0)' },
                    to: { transform: 'translateX(-100%)' },
                }
            },
            colors: {
                primary: {
                    50: '#fff7e6',
                    100: '#ffefd1',
                    200: '#ffe0a3',
                    300: '#ffd174',
                    400: '#ffbf46',
                    500: '#FFA900', // Base color #FFA900
                    600: '#e68a00',
                    700: '#cc7300',
                    800: '#b26600',
                    900: '#995900',
                },
                secondary: {
                    50: '#fff7ed',
                    100: '#ffedd5',
                    200: '#fed7aa',
                    300: '#fdba74',
                    400: '#fb923c',
                    500: '#f97316', // Orange 500
                    600: '#ea580c',
                    700: '#c2410c',
                    800: '#9a3412',
                    900: '#7c2d12',
                    950: '#431407',
                },
            },
        },
    },


    plugins: [forms,
        require('@tailwindcss/typography'),
        daisyui,
    ],



    daisyui: {
        themes: [
            {
                light: {
                    ...require("daisyui/src/theming/themes")["light"],
                    "primary": "#FFA900",  // Use your base primary color
                    "primary-focus": "#e68a00",  // Could use your 600 shade
                    "primary-content": "#ffffff",  // Text color on primary background
                    "secondary": "#f97316",  // Orange 500
                    "secondary-focus": "#ea580c",  // Orange 600
                    "secondary-content": "#ffffff",  // Text color on secondary background
                },
                dark: {
                    ...require("daisyui/src/theming/themes")["dark"],
                    "primary": "#FFA900",  // Use your base primary color
                    "primary-focus": "#e68a00",  // Could use your 600 shade
                    "primary-content": "#202020",  // Text color on primary background
                    "secondary": "#f97316",  // Orange 500
                    "secondary-focus": "#ea580c",  // Orange 600
                    "secondary-content": "#ffffff",  // Text color on secondary background
                },
            }
        ],
    },


};
