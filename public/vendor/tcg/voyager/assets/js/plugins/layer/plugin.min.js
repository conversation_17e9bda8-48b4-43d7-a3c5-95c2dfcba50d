tinymce.PluginManager.add("layer",function(e){function t(e){do if(e.className&&-1!=e.className.indexOf("mceItemLayer"))return e;while(e=e.parentNode)}function n(t){var n=e.dom;tinymce.each(n.select("div,p",t),function(e){/^(absolute|relative|fixed)$/i.test(e.style.position)&&(e.hasVisual?n.addClass(e,"mceItemVisualAid"):n.removeClass(e,"mceItemVisualAid"),n.addClass(e,"mceItemLayer"))})}function r(n){var r,i,o=[],a=t(e.selection.getNode()),s=-1,l=-1;for(i=[],tinymce.walk(e.getBody(),function(e){1==e.nodeType&&/^(absolute|relative|static)$/i.test(e.style.position)&&i.push(e)},"childNodes"),r=0;r<i.length;r++)o[r]=i[r].style.zIndex?parseInt(i[r].style.zIndex,10):0,0>s&&i[r]==a&&(s=r);if(0>n){for(r=0;r<o.length;r++)if(o[r]<o[s]){l=r;break}l>-1?(i[s].style.zIndex=o[l],i[l].style.zIndex=o[s]):o[s]>0&&(i[s].style.zIndex=o[s]-1)}else{for(r=0;r<o.length;r++)if(o[r]>o[s]){l=r;break}l>-1?(i[s].style.zIndex=o[l],i[l].style.zIndex=o[s]):i[s].style.zIndex=o[s]+1}e.execCommand("mceRepaint")}function i(){var t=e.dom,n=t.getPos(t.getParent(e.selection.getNode(),"*")),r=e.getBody();e.dom.add(r,"div",{style:{position:"absolute",left:n.x,top:n.y>20?n.y:20,width:100,height:100},"class":"mceItemVisualAid mceItemLayer"},e.selection.getContent()||e.getLang("layer.content")),tinymce.Env.ie&&t.setHTML(r,r.innerHTML)}function o(){var n=t(e.selection.getNode());n||(n=e.dom.getParent(e.selection.getNode(),"DIV,P,IMG")),n&&("absolute"==n.style.position.toLowerCase()?(e.dom.setStyles(n,{position:"",left:"",top:"",width:"",height:""}),e.dom.removeClass(n,"mceItemVisualAid"),e.dom.removeClass(n,"mceItemLayer")):(n.style.left||(n.style.left="20px"),n.style.top||(n.style.top="20px"),n.style.width||(n.style.width=n.width?n.width+"px":"100px"),n.style.height||(n.style.height=n.height?n.height+"px":"100px"),n.style.position="absolute",e.dom.setAttrib(n,"data-mce-style",""),e.addVisual(e.getBody())),e.execCommand("mceRepaint"),e.nodeChanged())}e.addCommand("mceInsertLayer",i),e.addCommand("mceMoveForward",function(){r(1)}),e.addCommand("mceMoveBackward",function(){r(-1)}),e.addCommand("mceMakeAbsolute",function(){o()}),e.addButton("moveforward",{title:"layer.forward_desc",cmd:"mceMoveForward"}),e.addButton("movebackward",{title:"layer.backward_desc",cmd:"mceMoveBackward"}),e.addButton("absolute",{title:"layer.absolute_desc",cmd:"mceMakeAbsolute"}),e.addButton("insertlayer",{title:"layer.insertlayer_desc",cmd:"mceInsertLayer"}),e.on("init",function(){tinymce.Env.ie&&e.getDoc().execCommand("2D-Position",!1,!0)}),e.on("mouseup",function(n){var r=t(n.target);r&&e.dom.setAttrib(r,"data-mce-style","")}),e.on("mousedown",function(n){var r,i=n.target,o=e.getDoc();tinymce.Env.gecko&&(t(i)?"on"!==o.designMode&&(o.designMode="on",i=o.body,r=i.parentNode,r.removeChild(i),r.appendChild(i)):"on"==o.designMode&&(o.designMode="off"))}),e.on("NodeChange",n)});