tinymce.PluginManager.add("importcss",function(e){function t(e){var t=tinymce.Env.cacheSuffix;return"string"==typeof e&&(e=e.replace("?"+t,"").replace("&"+t,"")),e}function n(t){var n=e.settings,r=n.skin!==!1&&(n.skin||"lightgray");if(r){var i=n.skin_url;return i=i?e.documentBaseURI.toAbsolute(i):tinymce.baseURL+"/skins/"+r,t===i+"/content"+(e.inline?".inline":"")+".min.css"}return!1}function r(e){return"string"==typeof e?function(t){return t.indexOf(e)!==-1}:e instanceof RegExp?function(t){return e.test(t)}:e}function i(r,i){function o(e,r){var s,l=e.href;if(l=t(l),l&&i(l,r)&&!n(l)){p(e.imports,function(e){o(e,!0)});try{s=e.cssRules||e.rules}catch(e){}p(s,function(e){e.styleSheet?o(e.styleSheet,!0):e.selectorText&&p(e.selectorText.split(","),function(e){a.push(tinymce.trim(e))})})}}var a=[],s={};p(e.contentCSS,function(e){s[e]=!0}),i||(i=function(e,t){return t||s[e]});try{p(r.styleSheets,function(e){o(e)})}finally{}return a}function o(t){var n,r=/^(?:([a-z0-9\-_]+))?(\.[a-z0-9_\-\.]+)$/i.exec(t);if(r){var i=r[1],o=r[2].substr(1).split(".").join(" "),a=tinymce.makeMap("a,img");return r[1]?(n={title:t},e.schema.getTextBlockElements()[i]?n.block=i:e.schema.getBlockElements()[i]||a[i.toLowerCase()]?n.selector=i:n.inline=i):r[2]&&(n={inline:"span",title:t.substr(1),classes:o}),e.settings.importcss_merge_classes!==!1?n.classes=o:n.attributes={"class":o},n}}function a(e,t){return tinymce.util.Tools.grep(e,function(e){return!e.filter||e.filter(t)})}function s(e){return tinymce.util.Tools.map(e,function(e){return tinymce.util.Tools.extend({},e,{original:e,selectors:{},filter:r(e.filter),item:{text:e.title,menu:[]}})})}function l(e,t){return null===t||e.settings.importcss_exclusive!==!1}function c(t,n,r){return!(l(e,n)?t in r:t in n.selectors)}function u(t,n,r){l(e,n)?r[t]=!0:n.selectors[t]=!0}function d(t,n,r){var i,a=e.settings;return i=r&&r.selector_converter?r.selector_converter:a.importcss_selector_converter?a.importcss_selector_converter:o,i.call(t,n,r)}var f=this,p=tinymce.each;e.on("renderFormatsMenu",function(t){var n=e.settings,o={},l=r(n.importcss_selector_filter),m=t.control,h=s(n.importcss_groups),g=function(t,n){if(c(t,n,o)){u(t,n,o);var r=d(f,t,n);if(r){var i=r.name||tinymce.DOM.uniqueId();return e.formatter.register(i,r),tinymce.extend({},m.settings.itemDefaults,{text:r.title,format:i})}}return null};e.settings.importcss_append||m.items().remove(),p(i(t.doc||e.getDoc(),r(n.importcss_file_filter)),function(e){if(e.indexOf(".mce-")===-1&&(!l||l(e))){var t=a(h,e);if(t.length>0)tinymce.util.Tools.each(t,function(t){var n=g(e,t);n&&t.item.menu.push(n)});else{var n=g(e,null);n&&m.add(n)}}}),p(h,function(e){e.item.menu.length>0&&m.add(e.item)}),t.control.renderNew()}),f.convertSelectorToFormat=o});
