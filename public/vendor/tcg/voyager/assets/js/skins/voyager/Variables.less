// Variables
// Syntax: <control>-(<sub control>)-<bg|border|text>-(<state>)-(<extra>);
// Example: @btn-primary-bg-hover-hlight;

@prefix:                         mce;

// Default font
@font-family:                    "Helvetica Neue", Helvetica, Arial, sans-serif;
@font-size:                      14px;
@line-height:                    20px;
@has-gradients:                  false;
@has-radius:                     false;
@has-boxshadow:                  false;
@has-button-borders:             false;

// Text colors
@text:                           #333333;
@text-inverse:                   #ffffff;
@text-disabled:                  #aaaaaa;
@text-shadow:                    0 1px 1px hsla(hue(@text-inverse), saturation(@text-inverse), lightness(@text-inverse), 0.75);
@text-error:                     #aa0000;

// Button
@btn-text:                       #a3afb7;
@btn-text-shadow:                #ffffff;
@btn-border-top:                 rgba(0,0,0,0);
@btn-border-right:               rgba(0,0,0,0);
@btn-border-bottom:              rgba(0,0,0,0);
@btn-border-left:                rgba(0,0,0,0);
@btn-caret-border:               @btn-text;
@btn-text-disabled:              @text-disabled;
@btn-box-shadow:                 inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 2px rgba(0, 0, 0, .05);
@btn-box-shadow-active:          inset 0 2px 4px rgba(0, 0, 0, .15), 0 1px 2px rgba(0, 0, 0, .05);
@btn-box-disabled-opacity:       0.4;
@btn-bg:                         #ffffff;
@btn-bg-hlight:                  #ffffff;
@btn-bg-hover:                   darken(@btn-bg, 5%);
@btn-bg-hlight-hover:            darken(@btn-bg-hlight, 5%);
@btn-border-hover:               darken(@btn-bg, 20%);
@btn-border-active:              darken(@btn-bg, 20%);
@btn-padding:                    4px 8px;

@btn-primary-bg:                 #0088cc;
@btn-primary-bg-hlight:          #ffffff;
@btn-primary-bg-hover:           darken(@btn-primary-bg, 5%);
@btn-primary-bg-hover-hlight:    darken(@btn-primary-bg-hlight, 5%);
@btn-primary-text:               #ffffff;
@btn-primary-text-shadow:        #ff0000;
@btn-primary-border-top:         mix(@btn-border-top, @btn-primary-bg, 50%);
@btn-primary-border-right:       mix(@btn-border-right, @btn-primary-bg, 50%);
@btn-primary-border-bottom:      mix(@btn-border-bottom, @btn-primary-bg, 50%);
@btn-primary-border-left:        mix(@btn-border-left, @btn-primary-bg, 50%);
@btn-primary-border:             transparent;
@btn-primary-border-hover:       transparent;

// Button group
@btn-group-border-width:         1px;

// Menu
@menuitem-text:                  #333333;
@menu-bg:                        #ffffff;
@menu-margin:                    -1px 0 0;
@menu-border:                    rgba(0,0,0,0.2);
@menubar-border:                 mix(@panel-border, @panel-bg, 60%);
@menuitem-text-inverse:          #ffffff;
@menubar-bg-active:              darken(@btn-bg, 10%);
@menuitem-bg-hover:              #0081C2;
@menuitem-bg-selected:           #0088cc;
@menuitem-bg-selected-hlight:    #0077b3;
@menuitem-bg-disabled:           #CCC;
@menuitem-caret:                 @menuitem-text;
@menuitem-caret-selected:        @menuitem-text-inverse;
@menuitem-separator-top:         #cbcbcb;
@menuitem-separator-bottom:      #ffffff;
@menuitem-bg-active:             #c8def4;
@menuitem-text-active:           #333333;
@menuitem-preview-border-active: #aaaaaa;
@menubar-menubtn-text:           #333333;

// Panel
@panel-border:                   #eeeeee;
@panel-bg:                       #ffffff;
@panel-bg-hlight:                #ffffff;

// Tabs
@tab-border:                     #c5c5c5;
@tab-bg:                         #e3e3e3;
@tab-bg-hover:                   #fdfdfd;
@tab-bg-active:                  #fdfdfd;
@tabs-bg:						 #ffffff;

// Tooltip
@tooltip-bg:                     #000;
@tooltip-text:                   white;
@tooltip-font-size:              11px;

// Notification
@notification-font-size:         14px;
@notification-bg:                #f0f0f0;
@notification-border:            #cccccc;
@notification-text:              #333333;
@notification-success-bg:        #dff0d8;
@notification-success-border:    #d6e9c6;
@notification-success-text:      #3c763d;
@notification-info-bg:           #d9edf7;
@notification-info-border:       #779ecb;
@notification-info-text:         #31708f;
@notification-warning-bg:        #fcf8e3;
@notification-warning-border:    #faebcc;
@notification-warning-text:      #8a6d3b;
@notification-error-bg:          #f2dede;
@notification-error-border:      #ebccd1;
@notification-error-text:        #a94442;

// Window
@window-border:                  #eeeeee;
@window-head-border:             @window-border;
@window-head-close:              mix(@text, @window-bg, 60%);
@window-head-close-hover:        mix(@text, @window-bg, 40%);
@window-foot-border:             @window-border;
@window-foot-bg:                 @window-bg;
@window-fullscreen-bg:           #FFF;
@window-modalblock-bg:           #000;
@window-modalblock-opacity:      0.3;
@window-box-shadow:              0 3px 7px rgba(0, 0, 0, 0.3);
@window-bg:                      #ffffff;
@window-title-font-size:         20px;

// Popover
@popover-bg:                     @window-bg;
@popover-arrow-width:            10px;
@popover-arrow:                  @window-bg;
@popover-arrow-outer-width:      @popover-arrow-width + 1;
@popover-arrow-outer:            rgba(0, 0, 0, 0.25);

// Floatpanel
@floatpanel-box-shadow:          0 5px 10px rgba(0, 0, 0, .2);

// Checkbox
@checkbox-bg:                    @btn-bg;
@checkbox-bg-hlight:             @btn-bg-hlight;
@checkbox-box-shadow:            inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 2px rgba(0, 0, 0, .05);
@checkbox-border:                #c5c5c5;
@checkbox-border-focus:          #59a5e1;

// Path
@path-text:                      @text;
@path-bg-focus:                  #666;
@path-text-focus:                #fff;

// Textbox
@textbox-text-placeholder:       #aaa;
@textbox-box-shadow:             inset 0 1px 1px rgba(0, 0, 0, 0.075);
@textbox-bg:                     #ffffff;
@textbox-border:                 #c5c5c5;
@textbox-border-focus:           #59a5e1;

// Selectbox
@selectbox-bg:                   @textbox-bg;
@selectbox-border:               @textbox-border;

// Throbber
@throbber-bg:                    #fff url('img/loader.gif') no-repeat center center;

// Combobox
@combobox-border:                @textbox-border;

// Colorpicker
@colorpicker-border:             @textbox-border;
@colorpicker-hue-bg:             #fff;
@colorpicker-hue-border:         #333;

// Grid
@grid-bg-active:                 @menuitem-bg-active;
@grid-border-active:             #a1a1a1;
@grid-border:                    #d6d6d6;

// Misc
@colorbtn-backcolor-bg:          #bbbbbb;
@iframe-border:                  @panel-border;

// Slider
@slider-border:                  #aaaaaa;
@slider-bg:                      #eeeeee;
@slider-handle-border:           #bbbbbb;
@slider-handle-bg:               #dddddd;

// Progress
@progress-border:                #cccccc;
@progress-bar-bg:                #dfdfdf;
@progress-bar-bg-hlight:         #cccccc;
@progress-text:                  #333333;
@progress-text-shadow:           #ffffff;

// Flow layout
@flow-layout-spacing:            2px;
