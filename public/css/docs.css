/* General Styling (adjust as needed) */
body {
    font-family: "<PERSON><PERSON><PERSON>", serif;
    font-optical-sizing: auto;
    font-size: 12pt;
    line-height: 1.4;
}

h1, h2 {
    font-family: "<PERSON>o", serif;
    font-optical-sizing: auto;
    font-weight: 600;
    font-style: normal;
    font-variation-settings:
      "wdth" 200;
    margin-bottom: 0.5em;
    line-height: 1.2;
    text-transform: uppercase;
}

p {
    hyphens: auto;
    word-break: break-word;
    -webkit-hyphens: auto; /* For Safari */
    -moz-hyphens: auto; /* For Firefox */
    -ms-hyphens: auto; /* For Internet Explorer */
    hyphenate-character: "\2010"; /* Use a soft hyphen character */
    hyphenate-limit-chars: 6 3 2; /* Adjust limits as needed */
    hyphenate-limit-lines: 2; /* Limit consecutive hyphenated lines */
    hyphenate-limit-last: always; /* Hyphenate the last word on a page */
    hyphenate-limit-before: 3; /* Min chars before hyphen */
    hyphenate-limit-after: 2; /* Min chars after hyphen */
    hyphenate-limit-zone: 8%; /* Adjust zone size as needed */
    lang: ro; /* Specify Romanian language */
}

ol {
    list-style: none;
    padding-left: 0; /* Reset default padding */
}

/* Primary Counter (header-counter) */
body {
    counter-reset: header-counter;
}

h1 {
    margin-top: 2em;
}

h2 {
    counter-increment: header-counter;
    margin-top: 1em;
}

h2::before {
    content: counter(header-counter) ". ";
    font-weight: bold;
}

/* Secondary Counter (paragraph-counter) */
h2 {
    
    counter-reset: paragraph-counter; /* Reset at each H2 */
}

.paragraph-counter > li {
    padding-top: 0.2rem;
    counter-increment: paragraph-counter;
}

.paragraph-counter > li::before {
    content: counter(header-counter) "." counter(paragraph-counter) ". ";
    font-weight: bold;
}

/* Ternary Counter (line-counter) */
.line-counter {
    padding-top: 0.2rem;
}

.line-counter li {
    padding-left: 2rem;
    counter-increment: line-counter;
}

.line-counter li::before {
    content: counter(header-counter) "." counter(paragraph-counter) "." counter(line-counter) ".  ";
    font-size: 0.9rem;
    font-weight: bold;
}

.signature-div {
    width: 100%;
    margin: 0 auto; /* Center the div */
    padding-top: 10mm; /* Add some padding around the content */
    display: flex;
    flex-direction: row;
    justify-content: space-between; /* Distribute columns evenly */
}

.signature-column {
    width: 50%; /* Each column takes up 50% of the custom-div */
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
    padding: 10px; /* Add some padding around the text */
}

.signature-line {
    text-align: left; /* Align text to the left */
    margin-bottom: 5px; /* Add some space between lines */
}

.signature-header {
    font-weight: bold; /* Make the header bold */
}

.signature-name {
    font-weight: bold; /* Make the name bold */
    line-height: 1;
}

.signature-role {
    font-style: italic; /* Make the role italic */
    line-height: 1;
}

/* Ternary Counter (line-letter) */
.line-letter {
    counter-increment: line-letter;
}

.line-letter::before {
    content: lower-alpha(counter(line-letter)) ". ";
    font-weight: normal;
}

ul {
    list-style-type: disc; /* Or 'circle', 'square', etc. */
    padding-left: 20px; /* Adjust spacing as needed */
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

ul li {
    margin-bottom: 0.25em; /* Adjust spacing between list items */
}

#runningHeader{
    position:running(logoRunning);
    padding-top: 2rem;
    height: 25mm;
    padding-bottom: 2rem;
    margin-top: 3mm;
    margin-bottom: 3rem;
    border-bottom: 2mm solid #bcdb0f57;
  }

#runningFooter{
    position:running(footerRunning);
    font-size: 0.8rem;
    color: #666666;
  }
#runningHeader svg{
    width: 150px;
    position: absolute;
    z-index: 1000;

  }


@page {
    size: A4 portrait;
    margin-top: 35mm;
    margin-right: 15mm;
    margin-bottom: 20mm;
    margin-left: 25mm;
  
    @bottom-right {
     /* content: counter(page); */
     content: counter(page) " / " counter(pages);
    }

    @top-left {
        content: element(logoRunning);
      }
  
    @bottom-center {
      content: string(title);
      text-transform: uppercase;
    }

    h2 {
        break-before: page; /* Page break before each H2 */
    }

    h2 {
        break-after: avoid; /* Avoid page break after each H2 */
    }

    
        p {
            hyphens: auto;
            word-break: break-word;
            -webkit-hyphens: auto; /* For Safari */
            -moz-hyphens: auto; /* For Firefox */
            -ms-hyphens: auto; /* For Internet Explorer */
            hyphenate-character: "\2010"; /* Use a soft hyphen character */
            hyphenate-limit-chars: 6 3 2; /* Adjust limits as needed */
            hyphenate-limit-lines: 2; /* Limit consecutive hyphenated lines */
            hyphenate-limit-last: always; /* Hyphenate the last word on a page */
            hyphenate-limit-before: 3; /* Min chars before hyphen */
            hyphenate-limit-after: 2; /* Min chars after hyphen */
            hyphenate-limit-zone: 8%; /* Adjust zone size as needed */
            lang: ro; /* Specify Romanian language */
        }
    
  
  }
  
  h1#title {
      string-set: title content(text);
  }
