<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Route;
use Wave\Facades\Wave;
//use App\Events\TestEvent;
//use App\Events\PrivateTestEvent;
use App\Http\Controllers\PdfableController;

// Wave routes
Wave::routes();

// Temporary debug routes for social authentication
Route::get('auth/{driver}/callback', '\App\Http\Controllers\SimpleSocialController@callback');
Route::get('auth/{driver}/redirect', '\App\Http\Controllers\SimpleSocialController@redirect');

Route::get('/set-session', function () {
    session(['step' => 2]);
    return 'Session variable "step" is set to 2';
});


// Add the buy-credits route


// Route::get('/broadcast-test', function () {
//     broadcast(new TestEvent('Blah blah blah'));
//     return 'Event fired';
// });

// Route::get('/private-broadcast-test/{id}', function ($id) {
//     $message = 'Hello from the private channel!';
//     event(new PrivateTestEvent($id, $message));

//     return "Private event fired for user ID: {$id}";
// });

//Route::middleware(['auth', 'editor.step'])->group(function () {
    // Any additional middleware-protected routes can go here
//});

Route::get('pdf/{pdfable}/generate', [PdfableController::class, 'generate'])
    ->name('pdf.generate')
    ->middleware(['web']); // Ensure session middleware is applied
// Document routes
Route::delete('/documents/{document}', [App\Http\Controllers\DocumentController::class, 'delete'])->name('documents.delete');
Route::get('/documents/{document}/payment', [App\Http\Controllers\DocumentPaymentController::class, 'show'])->name('documents.payment');
Route::get('/documents/{document}/download', [App\Http\Controllers\DocumentPaymentController::class, 'download'])->name('documents.download');

