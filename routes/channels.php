<?php
use App\Models\User;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Auth;
// /use App\Models\ChatSession;
/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.User.{id}', function ($user, $id) {
    //return true;
    if (Auth::check() && Auth::user()->id == $id) {
       // dd('true');
        return true;
    }

   // return (int) $user->id === (int) $id;
});
Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    //return true;
    if (Auth::check() && Auth::user()->id == $id) {
       // dd('true');
        return true;
    }

   // return (int) $user->id === (int) $id;
});