models:
  TemplateTier:
    table: template_tiers
    name: string
    # Additional fields as needed...

  DocumentCategory:
    table: document_categories
    name: string
    slug: string
    tier_id: id foreign:template_tiers
    is_free: boolean default:false
    relationships:
      hasMany: DocumentSubcategory

  DocumentSubcategory:
    table: document_subcategories
    category_id: id foreign:document_categories
    name: string
    is_free: boolean default:foreign:document_categories.is_free
    tier_id: id foreign:template_tiers

  DocumentBrick:
    table: document_bricks
    softDeletes: true
    title: string
    content: text:nullable

  Template:
    table: templates
    softDeletes: true
    name: string
    description: text:nullable
    category_id: id foreign:document_categories
    subcategory_id: id foreign:document_subcategories
    tier_id: id foreign:template_tiers
    relationships:
      belongsToMany: DocumentBrick

  AssemblyBlock:
    table: assembly_blocks
    template_id: id foreign:templates
    document_brick_id: id foreign:document_bricks
    order_number: integer:nullable

  Design:
    name: string
    view_path: string
    tier: string
    relationships:
      hasMany: Document

  Document:
    table: documents
    softDeletes: true
    # Fillable fields:
    user_id: id foreign:users
    template_id: id foreign:templates
    subcategory_id: id foreign:document_subcategories
    category_id: id foreign:document_categories
    document_number: string
    status: string
    transaction_id: integer:nullable
    client_info_profile_id: integer:nullable
    partner_info_profile_id: integer:nullable
    third_party_info_profile_id: integer:nullable
    design_id: id foreign:designs default:1
    thumbnail: string:nullable
    uuid: string
    document_url: string:nullable

  InfoProfile:
    table: info_profiles
    user_id: id foreign:users
    entity_type: enum:individual,srl,sa,ifam,asoc,ong default:individual
    email: string:nullable
    phone: string:nullable
    company_name: string:nullable
    address: string:nullable
    tax_identification_number: string:nullable
    registration_number: string:nullable
    registration_county: string:nullable
    date_of_birth: date:nullable
    place_of_birth: string:nullable
    county_of_birth: string:nullable
    pronoun: string:nullable
    first_name: string:nullable
    last_name: string:nullable
    id_document_type: string:nullable
    id_document_series: string:nullable
    id_document_number: string:nullable
    id_document_issuer: string:nullable
    id_document_issue_date: date:nullable
    personal_id_number: string:nullable
    bank_name: string:nullable
    bank_city: string:nullable
    iban: string:nullable
    is_temp: boolean:default(true)
    address_street: string:nullable
    address_number: string:nullable
    address_block: string:nullable
    address_entrance: string:nullable
    address_floor: string:nullable
    address_apt: string:nullable
    address_city: string:nullable
    address_county: string:nullable
    address_country: string:nullable
    registration_number_jud: string:nullable
    registration_number_nr: string:nullable
    registration_number_an: string:nullable

  DocumentDraft:
    user_id: id foreign:users
    template_id: id foreign:templates
    part_a: id foreign:info_profiles
    part_b: id foreign:info_profiles
    part_c: id foreign:info_profiles
    variables: json:nullable
    content: text:nullable
    final_content: text:nullable
